'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  Search, 
  Plus,
  Eye,
  Edit,
  Trash2,
  Phone,
  Mail,
  Calendar,
  DollarSign,
  MapPin
} from 'lucide-react';
import { useDashboardStore } from '@/lib/store/dashboard-store';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { cn } from '@/lib/utils';

export function ClientManagement() {
  const { clients, ui, actions } = useDashboardStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedClient, setSelectedClient] = useState<any>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Filter clients based on search
  const filteredClients = clients.filter(client => {
    const searchLower = searchQuery.toLowerCase();
    return (
      client.name.toLowerCase().includes(searchLower) ||
      client.email?.toLowerCase().includes(searchLower) ||
      client.phone?.toLowerCase().includes(searchLower)
    );
  });

  const openClientDetails = (client: any) => {
    setSelectedClient(client);
    setShowDetails(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
          <p className="text-gray-600">Manage your client relationships and history</p>
        </div>
        <Button className="bg-gradient-to-r from-rose-500 to-amber-500 hover:from-rose-600 hover:to-amber-600">
          <Plus className="w-4 h-4 mr-2" />
          Add Client
        </Button>
      </div>

      {/* Search */}
      <Card className="glass border-white/20">
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search clients by name, email, or phone..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 glass border-white/20"
            />
          </div>
        </CardContent>
      </Card>

      {/* Clients Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence mode="popLayout">
          {filteredClients.length > 0 ? (
            filteredClients.map((client, index) => (
              <motion.div
                key={client.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.05 }}
                layout
              >
                <Card className="glass border-white/20 hover:border-white/30 transition-all duration-300 cursor-pointer"
                      onClick={() => openClientDetails(client)}>
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4 mb-4">
                      <Avatar className="w-12 h-12 border-2 border-white/20">
                        <AvatarFallback className="bg-gradient-to-br from-rose-500 to-amber-500 text-white font-semibold">
                          {client.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-gray-900 truncate">{client.name}</h3>
                        <p className="text-sm text-gray-600 truncate">
                          Client since {new Date(client.client_since).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      {client.email && (
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Mail className="w-4 h-4" />
                          <span className="truncate">{client.email}</span>
                        </div>
                      )}
                      {client.phone && (
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Phone className="w-4 h-4" />
                          <span>{client.phone}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-between mt-4 pt-4 border-t border-white/10">
                      <div className="flex items-center gap-4">
                        <div className="text-center">
                          <p className="text-lg font-semibold text-gray-900">{client.total_bookings}</p>
                          <p className="text-xs text-gray-600">Bookings</p>
                        </div>
                        <div className="text-center">
                          <p className="text-lg font-semibold text-gray-900">
                            Rs. {client.total_revenue.toLocaleString()}
                          </p>
                          <p className="text-xs text-gray-600">Revenue</p>
                        </div>
                      </div>
                      
                      <Badge variant="outline" className="text-xs">
                        {client.preferred_communication}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          ) : (
            <div className="col-span-full">
              <Card className="glass border-white/20">
                <CardContent className="p-12 text-center">
                  <Users className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No clients found</h3>
                  <p className="text-gray-600 mb-6">
                    {searchQuery 
                      ? 'Try adjusting your search query'
                      : 'Your clients will appear here as you add them or receive bookings'
                    }
                  </p>
                  <Button className="bg-gradient-to-r from-rose-500 to-amber-500">
                    <Plus className="w-4 h-4 mr-2" />
                    Add First Client
                  </Button>
                </CardContent>
              </Card>
            </div>
          )}
        </AnimatePresence>
      </div>

      {/* Client Details Dialog */}
      <ClientDetailsDialog 
        client={selectedClient}
        open={showDetails}
        onOpenChange={setShowDetails}
      />
    </div>
  );
}

// Client Details Dialog Component
function ClientDetailsDialog({ 
  client, 
  open, 
  onOpenChange 
}: {
  client: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  if (!client) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl glass border-white/20">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Client Profile - {client.name}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Client Header */}
          <div className="flex items-center gap-4">
            <Avatar className="w-16 h-16 border-2 border-white/20">
              <AvatarFallback className="bg-gradient-to-br from-rose-500 to-amber-500 text-white font-semibold text-lg">
                {client.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{client.name}</h2>
              <p className="text-gray-600">Client since {new Date(client.client_since).toLocaleDateString()}</p>
            </div>
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900">Contact Information</h4>
              <div className="space-y-2">
                {client.email && (
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-gray-500" />
                    <span>{client.email}</span>
                  </div>
                )}
                {client.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-gray-500" />
                    <span>{client.phone}</span>
                  </div>
                )}
                {client.address && (
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-500" />
                    <span>{client.address}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <span className="font-medium">Preferred Contact:</span>
                  <Badge variant="outline">{client.preferred_communication}</Badge>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900">Business Summary</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Total Bookings:</span>
                  <span className="font-semibold">{client.total_bookings}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Total Revenue:</span>
                  <span className="font-semibold">Rs. {client.total_revenue.toLocaleString()}</span>
                </div>
                {client.how_heard_about_us && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Referral Source:</span>
                    <span className="font-semibold">{client.how_heard_about_us}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Notes */}
          {client.notes && (
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900">Notes</h4>
              <div className="p-4 bg-gray-50 rounded-lg">
                <p className="text-gray-700">{client.notes}</p>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 pt-4 border-t border-white/10">
            <Button className="bg-gradient-to-r from-rose-500 to-amber-500">
              <Edit className="w-4 h-4 mr-2" />
              Edit Client
            </Button>
            <Button variant="outline">
              <Calendar className="w-4 h-4 mr-2" />
              View Bookings
            </Button>
            <Button variant="outline">
              <Phone className="w-4 h-4 mr-2" />
              Contact Client
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
