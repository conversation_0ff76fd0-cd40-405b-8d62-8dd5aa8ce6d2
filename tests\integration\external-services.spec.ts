import { test, expect } from '@playwright/test';
import { BookingFormPage, generateBookingData, interceptEmailJS } from '../utils/test-helpers';

/**
 * External Services Integration Tests
 * Tests EmailJS, WhatsApp, Google Calendar, and other external integrations
 */

test.describe('EmailJS Integration', () => {
  test('should send email notification for engagement booking @desktop', async ({ page }) => {
    const bookingData = generateBookingData('engagement');
    const emailInterceptor = await interceptEmailJS(page);
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Complete engagement booking', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
      await page.selectOption('[data-testid="outfit-changes"]', bookingData.outfitChanges);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify EmailJS integration', async () => {
      expect(emailInterceptor.wasEmailSent()).toBe(true);
      
      const emailData = emailInterceptor.getEmailData();
      expect(emailData).toMatchObject({
        client_name: bookingData.brideAName,
        client_email: bookingData.email,
        client_phone: bookingData.phoneNumber,
        event_type: 'engagement',
        package_name: bookingData.package,
        venue_location: bookingData.desiredLocation,
        is_engagement: true,
        is_wedding: false,
        is_homecoming: false,
        is_combo: false
      });

      // Verify booking reference is included
      expect(emailData.booking_reference).toMatch(/^(BKG-\d{8}-\d{3}|LOCAL-\d+)$/);
      
      // Verify calendar link is generated
      expect(emailData.calendar_link).toContain('calendar.google.com');
      expect(emailData.calendar_link).toContain(encodeURIComponent(bookingData.brideAName));
      
      // Verify submission timestamp
      expect(emailData.submission_time).toBeDefined();
    });

    await test.step('Verify email confirmation on UI', async () => {
      await expect(page.locator('[data-testid="email-sent-confirmation"]')).toContainText('Email sent successfully');
    });
  });

  test('should send email notification for wedding booking @desktop', async ({ page }) => {
    const bookingData = generateBookingData('wedding');
    const emailInterceptor = await interceptEmailJS(page);
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Complete wedding booking', async () => {
      await bookingPage.fillStep1('wedding');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="registration-time"]', bookingData.registrationTime);
      await page.fill('[data-testid="ceremony-time"]', bookingData.ceremonyTime);
      await page.fill('[data-testid="event-end-time"]', bookingData.eventEndTime);
      await page.fill('[data-testid="guest-count"]', bookingData.guestCount);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify wedding-specific email data', async () => {
      expect(emailInterceptor.wasEmailSent()).toBe(true);
      
      const emailData = emailInterceptor.getEmailData();
      expect(emailData).toMatchObject({
        event_type: 'wedding',
        is_wedding: true,
        is_engagement: false,
        venue_location: bookingData.venue,
        registration_time: bookingData.registrationTime,
        ceremony_time: bookingData.ceremonyTime,
        event_end_time: bookingData.eventEndTime,
        guest_count: bookingData.guestCount
      });
    });
  });

  test('should send email notification for triple combo booking @desktop', async ({ page }) => {
    const bookingData = generateBookingData('triple-combo');
    const emailInterceptor = await interceptEmailJS(page);
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Complete triple combo booking', async () => {
      await bookingPage.fillStep1('triple-combo');
      await bookingPage.fillStep2(bookingData.package);
      
      // Fill all three event details
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
      await page.fill('[data-testid="wedding-date"]', bookingData.weddingDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="homecoming-date"]', bookingData.homecomingDate);
      await page.fill('[data-testid="homecoming-venue"]', bookingData.homecomingVenue);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify triple combo email data', async () => {
      expect(emailInterceptor.wasEmailSent()).toBe(true);
      
      const emailData = emailInterceptor.getEmailData();
      expect(emailData).toMatchObject({
        event_type: 'triple-combo',
        is_triple_combo: true,
        is_combo: true
      });

      // Should have calendar links for all three events
      expect(emailData.calendar_link_engagement).toContain('calendar.google.com');
      expect(emailData.calendar_link_wedding).toContain('calendar.google.com');
      expect(emailData.calendar_link_homecoming).toContain('calendar.google.com');
    });
  });

  test('should handle EmailJS service failure gracefully @desktop', async ({ page }) => {
    const bookingData = generateBookingData('engagement');
    
    // Simulate EmailJS service failure
    await page.route('**/api.emailjs.com/**', (route) => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Service unavailable' })
      });
    });
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Submit booking with EmailJS failure', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
    });

    await test.step('Verify graceful failure handling', async () => {
      // Should still show success message
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
      
      // Should show email failure warning
      await expect(page.locator('[data-testid="email-warning"]')).toContainText('email notification failed');
      
      // Should still provide WhatsApp option
      await expect(page.locator('[data-testid="whatsapp-button"]')).toBeVisible();
    });
  });
});

test.describe('WhatsApp Integration', () => {
  test('should generate correct WhatsApp URL for engagement @desktop', async ({ page }) => {
    const bookingData = generateBookingData('engagement');
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Complete engagement booking', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify WhatsApp URL generation', async () => {
      const whatsappButton = page.locator('[data-testid="whatsapp-button"]');
      await expect(whatsappButton).toBeVisible();
      
      const whatsappUrl = await whatsappButton.getAttribute('href');
      expect(whatsappUrl).toContain('wa.me/94715768552');
      expect(whatsappUrl).toContain('text=');
      
      // Decode and verify message content
      const urlParams = new URL(whatsappUrl!);
      const message = decodeURIComponent(urlParams.searchParams.get('text') || '');
      
      expect(message).toContain('Engagement Session Inquiry');
      expect(message).toContain(bookingData.brideAName);
      expect(message).toContain(bookingData.groomName);
      expect(message).toContain(bookingData.email);
      expect(message).toContain(bookingData.phoneNumber);
      expect(message).toContain(bookingData.eventDate);
      expect(message).toContain(bookingData.desiredLocation);
      expect(message).toContain(bookingData.package);
    });

    await test.step('Test WhatsApp button click', async () => {
      // Mock window.open to capture the URL
      await page.addInitScript(() => {
        window.open = (url: string) => {
          (window as any).lastOpenedUrl = url;
          return null;
        };
      });

      await page.click('[data-testid="whatsapp-button"]');
      
      const openedUrl = await page.evaluate(() => (window as any).lastOpenedUrl);
      expect(openedUrl).toContain('wa.me/94715768552');
    });
  });

  test('should generate correct WhatsApp URL for wedding @desktop', async ({ page }) => {
    const bookingData = generateBookingData('wedding');
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Complete wedding booking', async () => {
      await bookingPage.fillStep1('wedding');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="guest-count"]', bookingData.guestCount);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify wedding-specific WhatsApp message', async () => {
      const whatsappButton = page.locator('[data-testid="whatsapp-button"]');
      const whatsappUrl = await whatsappButton.getAttribute('href');
      
      const urlParams = new URL(whatsappUrl!);
      const message = decodeURIComponent(urlParams.searchParams.get('text') || '');
      
      expect(message).toContain('Wedding Photography Inquiry');
      expect(message).toContain(bookingData.venue);
      expect(message).toContain(bookingData.guestCount);
      expect(message).toContain('Registration:');
      expect(message).toContain('Ceremony:');
    });
  });

  test('should handle WhatsApp number configuration @desktop', async ({ page }) => {
    // Test with different WhatsApp number configuration
    await page.addInitScript(() => {
      process.env.NEXT_PUBLIC_DEFAULT_WHATSAPP = '94701234567';
    });
    
    const bookingData = generateBookingData('homecoming');
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Complete homecoming booking', async () => {
      await bookingPage.fillStep1('homecoming');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="homecoming-date"]', bookingData.homecomingDate);
      await page.fill('[data-testid="homecoming-venue"]', bookingData.homecomingVenue);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify configured WhatsApp number', async () => {
      const whatsappButton = page.locator('[data-testid="whatsapp-button"]');
      const whatsappUrl = await whatsappButton.getAttribute('href');
      
      // Should use the configured number (fallback to default if not set)
      expect(whatsappUrl).toMatch(/wa\.me\/947\d{8}/);
    });
  });
});

test.describe('Google Calendar Integration', () => {
  test('should generate correct calendar links for engagement @desktop', async ({ page }) => {
    const bookingData = generateBookingData('engagement');
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Complete engagement booking', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify calendar link generation', async () => {
      const calendarLink = page.locator('[data-testid="calendar-link"]');
      await expect(calendarLink).toBeVisible();
      
      const calendarUrl = await calendarLink.getAttribute('href');
      expect(calendarUrl).toContain('calendar.google.com/calendar/render');
      expect(calendarUrl).toContain('action=TEMPLATE');
      
      // Verify event details in calendar link
      const url = new URL(calendarUrl!);
      const params = url.searchParams;
      
      expect(params.get('text')).toContain(bookingData.brideAName);
      expect(params.get('text')).toContain('Engagement Session');
      expect(params.get('location')).toBe(bookingData.desiredLocation);
      expect(params.get('details')).toContain('Pre-wedding engagement photography');
    });

    await test.step('Test calendar link click', async () => {
      await page.addInitScript(() => {
        window.open = (url: string) => {
          (window as any).lastCalendarUrl = url;
          return null;
        };
      });

      await page.click('[data-testid="calendar-link"]');
      
      const openedUrl = await page.evaluate(() => (window as any).lastCalendarUrl);
      expect(openedUrl).toContain('calendar.google.com');
    });
  });

  test('should generate multiple calendar links for combo packages @desktop', async ({ page }) => {
    const bookingData = generateBookingData('wedding-homecoming');
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Complete dual combo booking', async () => {
      await bookingPage.fillStep1('wedding-homecoming');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="wedding-date"]', bookingData.weddingDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="homecoming-date"]', bookingData.homecomingDate);
      await page.fill('[data-testid="homecoming-venue"]', bookingData.homecomingVenue);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify multiple calendar links', async () => {
      // Should have separate calendar links for wedding and homecoming
      const weddingCalendarLink = page.locator('[data-testid="calendar-link-wedding"]');
      const homecomingCalendarLink = page.locator('[data-testid="calendar-link-homecoming"]');
      
      await expect(weddingCalendarLink).toBeVisible();
      await expect(homecomingCalendarLink).toBeVisible();
      
      const weddingUrl = await weddingCalendarLink.getAttribute('href');
      const homecomingUrl = await homecomingCalendarLink.getAttribute('href');
      
      // Wedding calendar link
      expect(weddingUrl).toContain('Wedding');
      expect(weddingUrl).toContain(encodeURIComponent(bookingData.venue));
      
      // Homecoming calendar link
      expect(homecomingUrl).toContain('Homecoming');
      expect(homecomingUrl).toContain(encodeURIComponent(bookingData.homecomingVenue));
      
      // Should have different dates
      expect(weddingUrl).not.toBe(homecomingUrl);
    });
  });

  test('should handle timezone in calendar links @desktop', async ({ page }) => {
    const bookingData = generateBookingData('wedding');
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Complete wedding booking with specific times', async () => {
      await bookingPage.fillStep1('wedding');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="registration-time"]', '08:00');
      await page.fill('[data-testid="ceremony-time"]', '11:00');
      await page.fill('[data-testid="event-end-time"]', '18:00');
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify timezone handling in calendar link', async () => {
      const calendarLink = page.locator('[data-testid="calendar-link"]');
      const calendarUrl = await calendarLink.getAttribute('href');
      
      const url = new URL(calendarUrl!);
      const params = url.searchParams;
      
      // Should include timezone parameter
      expect(params.get('ctz')).toBe('Asia/Colombo');
      
      // Should have properly formatted dates
      const dates = params.get('dates');
      expect(dates).toMatch(/^\d{8}T\d{6}\/\d{8}T\d{6}$/);
    });
  });
});
