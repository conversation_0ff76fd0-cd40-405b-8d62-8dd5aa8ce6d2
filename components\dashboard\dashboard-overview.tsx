'use client';

import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { 
  Calendar, 
  Users, 
  TrendingUp, 
  Clock,
  CheckCircle,
  AlertCircle,
  DollarSign,
  Camera
} from 'lucide-react';
import { useBookings } from '@/lib/hooks/use-bookings';
import { useAuth } from '@/lib/auth/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { cn } from '@/lib/utils';

const statsCards = [
  {
    title: 'Total Bookings',
    icon: Calendar,
    color: 'from-blue-500 to-blue-600',
    bgColor: 'bg-blue-50',
    textColor: 'text-blue-700'
  },
  {
    title: 'Active Clients',
    icon: Users,
    color: 'from-green-500 to-green-600',
    bgColor: 'bg-green-50',
    textColor: 'text-green-700'
  },
  {
    title: 'This Month Revenue',
    icon: DollarSign,
    color: 'from-purple-500 to-purple-600',
    bgColor: 'bg-purple-50',
    textColor: 'text-purple-700'
  },
  {
    title: 'Pending Bookings',
    icon: Clock,
    color: 'from-amber-500 to-amber-600',
    bgColor: 'bg-amber-50',
    textColor: 'text-amber-700'
  }
];

export function DashboardOverview() {
  const { profile } = useAuth();
  const { data: bookings, isLoading } = useBookings();
  const statsRef = useRef<HTMLDivElement>(null);

  // GSAP animations for premium feel
  useEffect(() => {
    if (statsRef.current && !isLoading) {
      const cards = statsRef.current.children;
      gsap.fromTo(
        cards,
        { 
          opacity: 0, 
          y: 30,
          scale: 0.9
        },
        { 
          opacity: 1, 
          y: 0,
          scale: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: 'power2.out'
        }
      );
    }
  }, [isLoading]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center space-y-4">
          <LoadingSpinner size="lg" />
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Calculate statistics
  const totalBookings = bookings?.length || 0;
  const pendingBookings = bookings?.filter(b => b.status === 'pending').length || 0;
  const confirmedBookings = bookings?.filter(b => b.status === 'confirmed').length || 0;
  const uniqueClients = new Set(bookings?.map(b => b.client_email).filter(Boolean)).size;
  
  // Calculate this month's revenue (mock data for now)
  const thisMonthRevenue = bookings?.reduce((total, booking) => {
    const bookingDate = new Date(booking.created_at);
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    if (bookingDate.getMonth() === currentMonth && bookingDate.getFullYear() === currentYear) {
      return total + (booking.package_price || 0);
    }
    return total;
  }, 0) || 0;

  const stats = [
    { value: totalBookings, change: '+12%', trend: 'up' },
    { value: uniqueClients, change: '+8%', trend: 'up' },
    { value: `Rs. ${thisMonthRevenue.toLocaleString()}`, change: '+15%', trend: 'up' },
    { value: pendingBookings, change: '-5%', trend: 'down' }
  ];

  const recentBookings = bookings?.slice(0, 5) || [];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="glass p-8 rounded-2xl border border-white/20 bg-gradient-to-br from-white/10 to-white/5"
      >
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-gradient-to-br from-rose-500 to-amber-500 rounded-2xl flex items-center justify-center">
            <Camera className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {profile?.owner_name || 'Photographer'}! 👋
            </h1>
            <p className="text-gray-600 mt-1">
              Here's what's happening with your photography business today.
            </p>
          </div>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <div ref={statsRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((card, index) => {
          const Icon = card.icon;
          const stat = stats[index];
          
          return (
            <Card key={card.title} className="glass border-white/20 hover:border-white/30 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className={cn('p-3 rounded-lg', card.bgColor)}>
                    <Icon className={cn('w-6 h-6', card.textColor)} />
                  </div>
                  <Badge 
                    variant={stat.trend === 'up' ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {stat.change}
                  </Badge>
                </div>
                <div className="mt-4">
                  <h3 className="text-2xl font-bold text-gray-900">{stat.value}</h3>
                  <p className="text-sm text-gray-600 mt-1">{card.title}</p>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Bookings */}
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Recent Bookings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentBookings.length > 0 ? (
              recentBookings.map((booking) => (
                <motion.div
                  key={booking.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center justify-between p-3 rounded-lg bg-white/5 border border-white/10"
                >
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      'w-2 h-2 rounded-full',
                      booking.status === 'confirmed' ? 'bg-green-500' :
                      booking.status === 'pending' ? 'bg-amber-500' : 'bg-gray-400'
                    )} />
                    <div>
                      <p className="font-medium text-gray-900">{booking.client_name}</p>
                      <p className="text-sm text-gray-600">{booking.event_type}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {booking.event_date && new Date(booking.event_date).toLocaleDateString()}
                    </p>
                    <Badge 
                      variant={booking.status === 'confirmed' ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      {booking.status}
                    </Badge>
                  </div>
                </motion.div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No bookings yet</p>
                <p className="text-sm">Your bookings will appear here</p>
              </div>
            )}
            
            {recentBookings.length > 0 && (
              <Button variant="outline" className="w-full mt-4">
                View All Bookings
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full justify-start" variant="outline" asChild>
              <a href="/dashboard/bookings?action=create">
                <Calendar className="w-4 h-4 mr-2" />
                Create New Booking
              </a>
            </Button>
            <Button className="w-full justify-start" variant="outline" asChild>
              <a href="/dashboard/clients?action=create">
                <Users className="w-4 h-4 mr-2" />
                Add New Client
              </a>
            </Button>
            <Button className="w-full justify-start" variant="outline" asChild>
              <a href="/dashboard/bookings?filter=pending">
                <CheckCircle className="w-4 h-4 mr-2" />
                Review Pending Bookings
              </a>
            </Button>
            <Button className="w-full justify-start" variant="outline" asChild>
              <a href="/dashboard/analytics">
                <TrendingUp className="w-4 h-4 mr-2" />
                View Analytics
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
