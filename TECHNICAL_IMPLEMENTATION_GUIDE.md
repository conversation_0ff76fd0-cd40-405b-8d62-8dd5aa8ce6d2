# Technical Implementation Guide
## Tera Works Photographer Booking System

---

## 1. API Endpoints & Database Functions

### Supabase RPC Functions

**1. Generate Booking Reference**
```sql
-- Function: generate_booking_reference()
-- Purpose: Creates unique booking references in format BKG-YYYYMMDD-XXX
-- Returns: TEXT

CREATE OR REPLACE FUNCTION generate_booking_reference()
RETURNS TEXT AS $$
DECLARE
    ref_prefix TEXT := 'BKG';
    date_part TEXT := TO_CHAR(NOW(), 'YYYYMMDD');
    random_part TEXT := LPAD(FLOOR(RANDOM() * 1000)::TEXT, 3, '0');
    full_reference TEXT;
BEGIN
    full_reference := ref_prefix || '-' || date_part || '-' || random_part;
    
    -- Ensure uniqueness
    WHILE EXISTS (SELECT 1 FROM bookings WHERE booking_reference = full_reference) LOOP
        random_part := LPAD(FLOOR(RANDOM() * 1000)::TEXT, 3, '0');
        full_reference := ref_prefix || '-' || date_part || '-' || random_part;
    END LOOP;
    
    RETURN full_reference;
END;
$$ LANGUAGE plpgsql;

-- Usage:
SELECT generate_booking_reference();
-- Returns: 'BKG-************'
```

**2. Log Analytics Event**
```sql
-- Function: log_analytics_event(event_type, event_data, booking_id, client_id)
-- Purpose: Records analytics events for business intelligence
-- Returns: UUID (event ID)

CREATE OR REPLACE FUNCTION log_analytics_event(
    p_event_type TEXT,
    p_event_data JSONB DEFAULT NULL,
    p_booking_id UUID DEFAULT NULL,
    p_client_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    event_id UUID;
BEGIN
    INSERT INTO analytics_events (event_type, event_data, booking_id, client_id)
    VALUES (p_event_type, p_event_data, p_booking_id, p_client_id)
    RETURNING id INTO event_id;
    
    RETURN event_id;
END;
$$ LANGUAGE plpgsql;

-- Usage examples:
SELECT log_analytics_event(
    'booking_submitted',
    '{"event_type": "wedding", "package": "premium", "source": "website"}',
    'booking-uuid-here',
    'client-uuid-here'
);

SELECT log_analytics_event(
    'dashboard_login',
    '{"login_method": "email", "user_agent": "Chrome/91.0"}'
);
```

**3. Auto-Create Client from Booking**
```sql
-- Trigger Function: create_client_from_booking()
-- Purpose: Automatically creates or links clients when bookings are inserted
-- Trigger: BEFORE INSERT ON bookings

CREATE OR REPLACE FUNCTION create_client_from_booking()
RETURNS TRIGGER AS $$
DECLARE
    existing_client_id UUID;
BEGIN
    -- Check if client already exists by email or phone
    SELECT id INTO existing_client_id 
    FROM clients 
    WHERE (email = NEW.client_email AND NEW.client_email IS NOT NULL)
       OR (phone = NEW.client_phone AND NEW.client_phone IS NOT NULL)
    LIMIT 1;
    
    IF existing_client_id IS NULL THEN
        -- Create new client
        INSERT INTO clients (name, email, phone, how_heard_about_us, total_bookings, client_since)
        VALUES (
            NEW.client_name,
            NEW.client_email,
            NEW.client_phone,
            (NEW.form_data->>'hearAbout'),
            1,
            CURRENT_DATE
        )
        RETURNING id INTO existing_client_id;
    ELSE
        -- Update existing client booking count
        UPDATE clients 
        SET total_bookings = total_bookings + 1,
            updated_at = NOW()
        WHERE id = existing_client_id;
    END IF;
    
    -- Link booking to client
    NEW.client_id := existing_client_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

---

## 2. REST API Patterns & Usage

### Booking Operations

**Create Booking (Public)**
```javascript
// Public booking submission (no authentication required)
const createBooking = async (formData) => {
  // Transform form data to database schema
  const bookingData = {
    booking_reference: await generateBookingReference(),
    client_name: formData.brideAName || formData.name,
    client_email: formData.email,
    client_phone: formData.phoneNumber,
    event_type: formData.eventType,
    event_date: formData.eventDate ? new Date(formData.eventDate).toISOString().split('T')[0] : null,
    venue: formData.venue,
    package_type: formData.package,
    client_notes: formData.additionalNotes,
    form_data: formData, // Complete form backup
    status: 'pending',
    email_sent: false,
    whatsapp_sent: false
  };

  const { data, error } = await supabase
    .from('bookings')
    .insert(bookingData)
    .select()
    .single();

  if (error) throw error;
  return data;
};
```

**Get Bookings (Authenticated)**
```javascript
// Fetch bookings with filtering and pagination
const getBookings = async (filters = {}) => {
  let query = supabase
    .from('bookings')
    .select(`
      id,
      booking_reference,
      client_name,
      client_email,
      client_phone,
      event_type,
      event_date,
      venue,
      package_type,
      status,
      created_at,
      clients (
        id,
        name,
        email,
        total_bookings
      )
    `);

  // Apply filters
  if (filters.status) {
    query = query.eq('status', filters.status);
  }

  if (filters.eventType) {
    query = query.eq('event_type', filters.eventType);
  }

  if (filters.dateRange) {
    query = query
      .gte('event_date', filters.dateRange.from)
      .lte('event_date', filters.dateRange.to);
  }

  if (filters.search) {
    query = query.or(`
      client_name.ilike.%${filters.search}%,
      client_email.ilike.%${filters.search}%,
      booking_reference.ilike.%${filters.search}%
    `);
  }

  // Pagination
  if (filters.page) {
    const limit = filters.limit || 20;
    const offset = (filters.page - 1) * limit;
    query = query.range(offset, offset + limit - 1);
  }

  // Ordering
  query = query.order('created_at', { ascending: false });

  const { data, error } = await query;
  if (error) throw error;
  return data;
};
```

**Update Booking Status**
```javascript
// Update booking status with automatic logging
const updateBookingStatus = async (bookingId, newStatus, notes = '') => {
  const { data, error } = await supabase
    .from('bookings')
    .update({
      status: newStatus,
      photographer_notes: notes,
      updated_at: new Date().toISOString()
    })
    .eq('id', bookingId)
    .select()
    .single();

  if (error) throw error;

  // Log status change
  await supabase.rpc('log_analytics_event', {
    p_event_type: 'booking_status_changed',
    p_event_data: {
      old_status: data.status,
      new_status: newStatus,
      notes: notes
    },
    p_booking_id: bookingId
  });

  return data;
};
```

### Client Operations

**Get Client with Booking History**
```javascript
const getClientWithHistory = async (clientId) => {
  const { data, error } = await supabase
    .from('clients')
    .select(`
      *,
      bookings (
        id,
        booking_reference,
        event_type,
        event_date,
        status,
        package_type,
        total_amount,
        created_at
      )
    `)
    .eq('id', clientId)
    .single();

  if (error) throw error;
  return data;
};
```

**Update Client Information**
```javascript
const updateClient = async (clientId, updates) => {
  const { data, error } = await supabase
    .from('clients')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', clientId)
    .select()
    .single();

  if (error) throw error;
  return data;
};
```

---

## 3. Authentication Flow Implementation

### Supabase Auth Setup

**Authentication Context**
```javascript
// lib/auth/auth-context.tsx
import { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/client';

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [session, setSession] = useState(null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch photographer profile
  const fetchProfile = async (userId) => {
    try {
      const { data, error } = await supabase
        .from('photographer_profile')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching profile:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      return null;
    }
  };

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const { data: { session: initialSession } } = await supabase.auth.getSession();
        
        setSession(initialSession);
        setUser(initialSession?.user ?? null);

        if (initialSession?.user) {
          const profileData = await fetchProfile(initialSession.user.id);
          setProfile(profileData);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          const profileData = await fetchProfile(session.user.id);
          setProfile(profileData);
        } else {
          setProfile(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  // Sign in
  const signIn = async (email, password) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      return { error };
    } catch (error) {
      return { error };
    }
  };

  // Sign up
  const signUp = async (email, password, profileData) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) return { error };

      // Create photographer profile
      if (data.user) {
        const { error: profileError } = await supabase
          .from('photographer_profile')
          .insert({
            id: data.user.id,
            email: data.user.email,
            ...profileData
          });

        if (profileError) {
          console.error('Error creating profile:', profileError);
        }
      }

      return { data, error: null };
    } catch (error) {
      return { error };
    }
  };

  // Sign out
  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) console.error('Error signing out:', error);
  };

  const value = {
    user,
    session,
    profile,
    loading,
    signIn,
    signUp,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
```

**Authentication Guard**
```javascript
// lib/auth/auth-guard.tsx
import { useAuth } from './auth-context';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export function AuthGuard({ 
  children, 
  requireAuth = true, 
  redirectTo = '/dashboard/login' 
}) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (requireAuth && !user) {
        router.push(redirectTo);
      } else if (!requireAuth && user) {
        router.push('/dashboard');
      }
    }
  }, [user, loading, requireAuth, redirectTo, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (requireAuth && !user) {
    return null; // Will redirect
  }

  if (!requireAuth && user) {
    return null; // Will redirect
  }

  return <>{children}</>;
}
```

---

## 4. Real-time Subscriptions Implementation

### Booking Real-time Updates

**Real-time Hook**
```javascript
// lib/hooks/use-realtime.js
import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/client';
import { useDashboardStore } from '@/lib/store/dashboard-store';

export function useRealtimeSubscriptions() {
  const [isConnected, setIsConnected] = useState(false);
  const { actions } = useDashboardStore();

  useEffect(() => {
    // Subscribe to booking changes
    const bookingSubscription = supabase
      .channel('bookings')
      .on('postgres_changes', 
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'bookings' 
        }, 
        (payload) => {
          console.log('New booking received:', payload.new);
          
          // Add to store
          actions.addBooking(payload.new);
          
          // Show notification
          actions.addNotification({
            id: `booking-${payload.new.id}`,
            type: 'booking',
            title: 'New Booking Received',
            message: `${payload.new.event_type} booking from ${payload.new.client_name}`,
            timestamp: new Date(),
            read: false,
            data: payload.new
          });
        }
      )
      .on('postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'bookings'
        },
        (payload) => {
          console.log('Booking updated:', payload.new);
          actions.updateBooking(payload.new.id, payload.new);
        }
      )
      .subscribe((status) => {
        setIsConnected(status === 'SUBSCRIBED');
      });

    // Subscribe to client changes
    const clientSubscription = supabase
      .channel('clients')
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'clients'
        },
        (payload) => {
          console.log('Client change:', payload);
          
          if (payload.eventType === 'INSERT') {
            actions.addClient(payload.new);
          } else if (payload.eventType === 'UPDATE') {
            actions.updateClient(payload.new.id, payload.new);
          }
        }
      )
      .subscribe();

    return () => {
      bookingSubscription.unsubscribe();
      clientSubscription.unsubscribe();
    };
  }, [actions]);

  return { isConnected };
}
```

**Connection Status Component**
```javascript
// components/ui/connection-status.tsx
import { useRealtimeSubscriptions } from '@/lib/hooks/use-realtime';
import { Wifi, WifiOff } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export function ConnectionStatus() {
  const { isConnected } = useRealtimeSubscriptions();

  return (
    <Badge 
      variant={isConnected ? 'default' : 'destructive'}
      className="flex items-center gap-1"
    >
      {isConnected ? (
        <>
          <Wifi className="w-3 h-3" />
          Connected
        </>
      ) : (
        <>
          <WifiOff className="w-3 h-3" />
          Disconnected
        </>
      )}
    </Badge>
  );
}
```

---

## 5. State Management with Zustand

**Dashboard Store**
```javascript
// lib/store/dashboard-store.ts
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

const useDashboardStore = create()(
  devtools(
    persist(
      (set, get) => ({
        // State
        bookings: [],
        clients: [],
        selectedBooking: null,
        selectedClient: null,
        ui: {
          sidebarCollapsed: false,
          activeView: 'dashboard',
          notifications: [],
          loading: {
            bookings: false,
            clients: false,
            analytics: false,
          },
          filters: {
            bookings: {
              status: [],
              eventType: [],
              dateRange: { from: null, to: null },
            },
          },
        },

        actions: {
          // Booking actions
          setBookings: (bookings) => set({ bookings }),
          
          addBooking: (booking) => set((state) => ({
            bookings: [booking, ...state.bookings],
          })),
          
          updateBooking: (id, updates) => set((state) => ({
            bookings: state.bookings.map((booking) =>
              booking.id === id ? { ...booking, ...updates } : booking
            ),
            selectedBooking: state.selectedBooking?.id === id 
              ? { ...state.selectedBooking, ...updates }
              : state.selectedBooking,
          })),
          
          selectBooking: (booking) => set({ selectedBooking: booking }),

          // Client actions
          setClients: (clients) => set({ clients }),
          
          addClient: (client) => set((state) => ({
            clients: [client, ...state.clients],
          })),
          
          updateClient: (id, updates) => set((state) => ({
            clients: state.clients.map((client) =>
              client.id === id ? { ...client, ...updates } : client
            ),
          })),

          // Notification actions
          addNotification: (notification) => set((state) => ({
            ui: {
              ...state.ui,
              notifications: [notification, ...state.ui.notifications],
            },
          })),
          
          markNotificationRead: (id) => set((state) => ({
            ui: {
              ...state.ui,
              notifications: state.ui.notifications.map((notification) =>
                notification.id === id 
                  ? { ...notification, read: true }
                  : notification
              ),
            },
          })),

          // UI actions
          toggleSidebar: () => set((state) => ({
            ui: {
              ...state.ui,
              sidebarCollapsed: !state.ui.sidebarCollapsed,
            },
          })),
          
          setActiveView: (view) => set((state) => ({
            ui: {
              ...state.ui,
              activeView: view,
            },
          })),
        },
      }),
      {
        name: 'photographer-dashboard-store',
        partialize: (state) => ({
          ui: {
            sidebarCollapsed: state.ui.sidebarCollapsed,
            filters: state.ui.filters,
          },
        }),
      }
    ),
    {
      name: 'photographer-dashboard',
    }
  )
);

export { useDashboardStore };
```

---

## Implementation Checklist

### ✅ Completed
- [x] Database schema deployed
- [x] RLS policies configured
- [x] Database functions created
- [x] Environment variables template
- [x] Authentication context setup
- [x] Real-time subscriptions framework
- [x] State management store

### 🔄 In Progress
- [ ] API key configuration
- [ ] Authentication testing
- [ ] Real-time connection testing

### 📋 Next Steps
1. **Configure API Keys**: Update `.env.local` with actual Supabase keys
2. **Test Authentication**: Create first photographer account
3. **Test Booking Flow**: Submit test booking and verify data flow
4. **Test Dashboard**: Login and verify real-time updates
5. **Deploy to Production**: Set up Vercel deployment with environment variables

This implementation guide provides the complete technical foundation for the photographer booking system. All components are designed to work together seamlessly with proper error handling and real-time capabilities.
