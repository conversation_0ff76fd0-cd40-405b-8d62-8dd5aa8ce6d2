-- Row Level Security Policies for Standalone Photographer Dashboard
-- Since this is a single-photographer system, <PERSON><PERSON> ensures only authenticated photographer can access data

-- Enable <PERSON><PERSON> on all tables
ALTER TABLE photographer_profile ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE communication_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE package_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;

-- Photographer Profile Policies
-- Only authenticated photographer can access their profile
CREATE POLICY "Photographer can view own profile" ON photographer_profile
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can update own profile" ON photographer_profile
  FOR UPDATE USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can insert own profile" ON photographer_profile
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Clients Policies
-- Authenticated photographer can manage all clients
CREATE POLICY "Photographer can view all clients" ON clients
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can insert clients" ON clients
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can update clients" ON clients
  FOR UPDATE USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can delete clients" ON clients
  FOR DELETE USING (auth.uid() IS NOT NULL);

-- Bookings Policies
-- Authenticated photographer can manage all bookings
CREATE POLICY "Photographer can view all bookings" ON bookings
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can insert bookings" ON bookings
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can update bookings" ON bookings
  FOR UPDATE USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can delete bookings" ON bookings
  FOR DELETE USING (auth.uid() IS NOT NULL);

-- Public booking submission policy (for the booking form)
-- Allow anonymous users to insert bookings (form submissions)
CREATE POLICY "Allow public booking submissions" ON bookings
  FOR INSERT WITH CHECK (true);

-- Communication Log Policies
CREATE POLICY "Photographer can view communication log" ON communication_log
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can insert communication log" ON communication_log
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can update communication log" ON communication_log
  FOR UPDATE USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can delete communication log" ON communication_log
  FOR DELETE USING (auth.uid() IS NOT NULL);

-- Package Templates Policies
CREATE POLICY "Photographer can view package templates" ON package_templates
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can insert package templates" ON package_templates
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can update package templates" ON package_templates
  FOR UPDATE USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can delete package templates" ON package_templates
  FOR DELETE USING (auth.uid() IS NOT NULL);

-- Public read access for package templates (for booking form)
CREATE POLICY "Allow public read of active packages" ON package_templates
  FOR SELECT USING (is_active = true);

-- Settings Policies
CREATE POLICY "Photographer can view settings" ON settings
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can insert settings" ON settings
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can update settings" ON settings
  FOR UPDATE USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can delete settings" ON settings
  FOR DELETE USING (auth.uid() IS NOT NULL);

-- Analytics Events Policies
CREATE POLICY "Photographer can view analytics" ON analytics_events
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Photographer can insert analytics" ON analytics_events
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Allow system to insert analytics events
CREATE POLICY "Allow system analytics insertion" ON analytics_events
  FOR INSERT WITH CHECK (true);

-- Functions for automatic updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_photographer_profile_updated_at BEFORE UPDATE ON photographer_profile FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_package_templates_updated_at BEFORE UPDATE ON package_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to generate booking reference
CREATE OR REPLACE FUNCTION generate_booking_reference()
RETURNS TEXT AS $$
DECLARE
    ref_prefix TEXT := 'BKG';
    date_part TEXT := TO_CHAR(NOW(), 'YYYYMMDD');
    random_part TEXT := LPAD(FLOOR(RANDOM() * 1000)::TEXT, 3, '0');
    full_reference TEXT;
BEGIN
    full_reference := ref_prefix || '-' || date_part || '-' || random_part;
    
    -- Ensure uniqueness
    WHILE EXISTS (SELECT 1 FROM bookings WHERE booking_reference = full_reference) LOOP
        random_part := LPAD(FLOOR(RANDOM() * 1000)::TEXT, 3, '0');
        full_reference := ref_prefix || '-' || date_part || '-' || random_part;
    END LOOP;
    
    RETURN full_reference;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically create client from booking if not exists
CREATE OR REPLACE FUNCTION create_client_from_booking()
RETURNS TRIGGER AS $$
DECLARE
    existing_client_id UUID;
BEGIN
    -- Check if client already exists by email or phone
    SELECT id INTO existing_client_id 
    FROM clients 
    WHERE (email = NEW.client_email AND NEW.client_email IS NOT NULL)
       OR (phone = NEW.client_phone AND NEW.client_phone IS NOT NULL)
    LIMIT 1;
    
    IF existing_client_id IS NULL THEN
        -- Create new client
        INSERT INTO clients (name, email, phone, how_heard_about_us, total_bookings, client_since)
        VALUES (
            NEW.client_name,
            NEW.client_email,
            NEW.client_phone,
            (NEW.form_data->>'hearAbout'),
            1,
            CURRENT_DATE
        )
        RETURNING id INTO existing_client_id;
    ELSE
        -- Update existing client booking count
        UPDATE clients 
        SET total_bookings = total_bookings + 1,
            updated_at = NOW()
        WHERE id = existing_client_id;
    END IF;
    
    -- Link booking to client
    NEW.client_id := existing_client_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-create clients from bookings
CREATE TRIGGER auto_create_client_from_booking
    BEFORE INSERT ON bookings
    FOR EACH ROW
    EXECUTE FUNCTION create_client_from_booking();

-- Function to log analytics events
CREATE OR REPLACE FUNCTION log_analytics_event(
    p_event_type TEXT,
    p_event_data JSONB DEFAULT NULL,
    p_booking_id UUID DEFAULT NULL,
    p_client_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    event_id UUID;
BEGIN
    INSERT INTO analytics_events (event_type, event_data, booking_id, client_id)
    VALUES (p_event_type, p_event_data, p_booking_id, p_client_id)
    RETURNING id INTO event_id;
    
    RETURN event_id;
END;
$$ LANGUAGE plpgsql;
