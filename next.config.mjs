/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  experimental: {
    appDir: true,
  },
  async rewrites() {
    return [
      // Dashboard routes
      {
        source: '/dashboard/:path*',
        destination: '/dashboard/:path*',
      },
      // Existing booking form routes (preserve current functionality)
      {
        source: '/',
        destination: '/booking',
      },
      {
        source: '/booking-success',
        destination: '/booking-success',
      },
    ];
  },
  async redirects() {
    return [
      // Redirect old routes to maintain compatibility
      {
        source: '/booking',
        destination: '/',
        permanent: false,
      },
    ];
  },
  images: {
    domains: ['localhost'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
  },
  env: {
    CUSTOM_KEY: 'photographer-dashboard',
  },
};

export default nextConfig;
