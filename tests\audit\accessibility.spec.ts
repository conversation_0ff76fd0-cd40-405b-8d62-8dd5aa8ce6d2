import { test, expect } from '@playwright/test';

/**
 * Accessibility Audit Tests
 * Tests WCAG compliance, keyboard navigation, screen reader support
 */

test.describe('WCAG Compliance', () => {
  test('should pass accessibility audit on booking form @desktop', async ({ page }) => {
    await page.goto('/');

    try {
      const { injectAxe, checkA11y } = await import('axe-playwright');
      await injectAxe(page);

      await test.step('Run accessibility audit on initial page', async () => {
        await checkA11y(page, null, {
          detailedReport: true,
          detailedReportOptions: { html: true }
        });
      });
    } catch (error) {
      console.warn('Accessibility testing not available:', error);
    }

    await test.step('Test form step accessibility', async () => {
      try {
        const { checkA11y } = await import('axe-playwright');

        // Test each step of the booking form
        await page.click('[data-testid="event-type-engagement"]');
        await checkA11y(page, '[data-testid="booking-form"]');

        await page.click('[data-testid="next-step"]');
        await checkA11y(page, '[data-testid="package-selection"]');

        await page.click('[data-testid="package-basic-engagement"]');
        await page.click('[data-testid="next-step"]');
        await checkA11y(page, '[data-testid="event-details"]');
      } catch (error) {
        console.warn('Accessibility testing not available:', error);
      }
    });
  });

  test('should pass accessibility audit on dashboard @desktop', async ({ page }) => {
    // Login first
    await page.goto('/dashboard/login');
    await page.fill('[data-testid="email"]', process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>');
    await page.fill('[data-testid="password"]', process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('/dashboard');

    try {
      const { injectAxe, checkA11y } = await import('axe-playwright');
      await injectAxe(page);

      await test.step('Run accessibility audit on dashboard', async () => {
        await checkA11y(page, null, {
          detailedReport: true,
          detailedReportOptions: { html: true }
        });
      });

      await test.step('Test dashboard navigation accessibility', async () => {
        await page.click('[data-testid="nav-bookings"]');
        await checkA11y(page, '[data-testid="bookings-page"]');

        await page.click('[data-testid="nav-clients"]');
        await checkA11y(page, '[data-testid="clients-page"]');
      });
    } catch (error) {
      console.warn('Accessibility testing not available:', error);
    }
  });

  test('should have proper heading hierarchy @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Check heading structure', async () => {
      const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
      const headingLevels: number[] = [];

      for (const heading of headings) {
        const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
        const level = parseInt(tagName.charAt(1));
        headingLevels.push(level);
      }

      // Should start with h1
      expect(headingLevels[0]).toBe(1);

      // Check for proper hierarchy (no skipping levels)
      for (let i = 1; i < headingLevels.length; i++) {
        const currentLevel = headingLevels[i];
        const previousLevel = headingLevels[i - 1];
        
        // Should not skip more than one level
        expect(currentLevel - previousLevel).toBeLessThanOrEqual(1);
      }
    });
  });

  test('should have proper form labels and descriptions @desktop', async ({ page }) => {
    await page.goto('/');
    
    // Navigate to contact form
    await page.click('[data-testid="event-type-engagement"]');
    await page.click('[data-testid="next-step"]');
    await page.click('[data-testid="package-basic-engagement"]');
    await page.click('[data-testid="next-step"]');
    await page.fill('[data-testid="event-date"]', '2025-12-25');
    await page.click('[data-testid="next-step"]');

    await test.step('Check form accessibility', async () => {
      // All form inputs should have labels
      const inputs = page.locator('input, select, textarea');
      const inputCount = await inputs.count();

      for (let i = 0; i < inputCount; i++) {
        const input = inputs.nth(i);
        const inputId = await input.getAttribute('id');
        const ariaLabel = await input.getAttribute('aria-label');
        const ariaLabelledBy = await input.getAttribute('aria-labelledby');

        // Should have either a label, aria-label, or aria-labelledby
        if (inputId) {
          const label = page.locator(`label[for="${inputId}"]`);
          const hasLabel = await label.count() > 0;
          
          expect(hasLabel || ariaLabel || ariaLabelledBy).toBeTruthy();
        }
      }
    });

    await test.step('Check required field indicators', async () => {
      const requiredInputs = page.locator('input[required], select[required], textarea[required]');
      const requiredCount = await requiredInputs.count();

      for (let i = 0; i < requiredCount; i++) {
        const input = requiredInputs.nth(i);
        const ariaRequired = await input.getAttribute('aria-required');
        const ariaInvalid = await input.getAttribute('aria-invalid');
        
        // Required inputs should have aria-required
        expect(ariaRequired).toBe('true');
      }
    });
  });

  test('should support high contrast mode @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Test high contrast compatibility', async () => {
      // Simulate high contrast mode
      await page.addStyleTag({
        content: `
          @media (prefers-contrast: high) {
            * {
              background: black !important;
              color: white !important;
              border-color: white !important;
            }
          }
        `
      });

      // Check if content is still visible and readable
      await expect(page.locator('[data-testid="booking-form"]')).toBeVisible();
      await expect(page.locator('[data-testid="event-type-engagement"]')).toBeVisible();
    });
  });
});

test.describe('Keyboard Navigation', () => {
  test('should support full keyboard navigation on booking form @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Navigate form using keyboard only', async () => {
      // Focus should start on first interactive element
      await page.keyboard.press('Tab');
      
      // Should be able to select event type with keyboard
      await page.keyboard.press('Enter');
      await expect(page.locator('[data-testid="event-type-engagement"]')).toHaveAttribute('data-selected', 'true');
      
      // Navigate to next step
      await page.keyboard.press('Tab');
      await page.keyboard.press('Enter');
      
      // Should be on package selection
      await expect(page.locator('[data-testid="package-selection"]')).toBeVisible();
    });

    await test.step('Test keyboard navigation in package selection', async () => {
      // Should be able to select package with keyboard
      await page.keyboard.press('Tab');
      await page.keyboard.press('Enter');
      
      // Navigate to next step
      await page.keyboard.press('Tab');
      await page.keyboard.press('Enter');
    });

    await test.step('Test form field navigation', async () => {
      // Fill event date
      await page.keyboard.press('Tab');
      await page.keyboard.type('2025-12-25');
      
      // Navigate to next field
      await page.keyboard.press('Tab');
      await page.keyboard.type('Test Location');
      
      // Continue to next step
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab'); // Skip to next button
      await page.keyboard.press('Enter');
    });
  });

  test('should support keyboard navigation on dashboard @desktop', async ({ page }) => {
    // Login first
    await page.goto('/dashboard/login');
    await page.fill('[data-testid="email"]', process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>');
    await page.fill('[data-testid="password"]', process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('/dashboard');

    await test.step('Navigate dashboard with keyboard', async () => {
      // Should be able to navigate menu with keyboard
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab'); // Navigate to menu
      
      // Use arrow keys to navigate menu items
      await page.keyboard.press('ArrowDown');
      await page.keyboard.press('Enter');
      
      // Should navigate to bookings page
      await expect(page).toHaveURL('/dashboard/bookings');
    });

    await test.step('Test table navigation', async () => {
      // Should be able to navigate booking table with keyboard
      await page.keyboard.press('Tab');
      
      // Should be able to open booking details with Enter
      await page.keyboard.press('Enter');
      await expect(page.locator('[data-testid="booking-details-modal"]')).toBeVisible();
      
      // Should be able to close modal with Escape
      await page.keyboard.press('Escape');
      await expect(page.locator('[data-testid="booking-details-modal"]')).not.toBeVisible();
    });
  });

  test('should have visible focus indicators @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Check focus indicators', async () => {
      const focusableElements = page.locator('button, input, select, textarea, a[href], [tabindex]:not([tabindex="-1"])');
      const count = await focusableElements.count();

      for (let i = 0; i < Math.min(count, 5); i++) { // Test first 5 elements
        const element = focusableElements.nth(i);
        await element.focus();
        
        // Check if element has visible focus indicator
        const focusStyles = await element.evaluate(el => {
          const styles = window.getComputedStyle(el, ':focus');
          return {
            outline: styles.outline,
            outlineWidth: styles.outlineWidth,
            boxShadow: styles.boxShadow
          };
        });
        
        // Should have some form of focus indicator
        const hasFocusIndicator = 
          focusStyles.outline !== 'none' || 
          focusStyles.outlineWidth !== '0px' || 
          focusStyles.boxShadow !== 'none';
        
        expect(hasFocusIndicator).toBeTruthy();
      }
    });
  });

  test('should handle skip links @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Test skip to main content link', async () => {
      // Press Tab to focus skip link
      await page.keyboard.press('Tab');
      
      const skipLink = page.locator('[data-testid="skip-to-main"]');
      if (await skipLink.isVisible()) {
        await page.keyboard.press('Enter');
        
        // Should focus main content
        const mainContent = page.locator('main, [role="main"], [data-testid="main-content"]');
        await expect(mainContent).toBeFocused();
      }
    });
  });
});

test.describe('Screen Reader Support', () => {
  test('should have proper ARIA labels and roles @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Check ARIA landmarks', async () => {
      // Should have main landmark
      await expect(page.locator('main, [role="main"]')).toBeVisible();
      
      // Should have navigation landmark
      await expect(page.locator('nav, [role="navigation"]')).toBeVisible();
      
      // Form should have proper role
      await expect(page.locator('form, [role="form"]')).toBeVisible();
    });

    await test.step('Check button accessibility', async () => {
      const buttons = page.locator('button, [role="button"]');
      const buttonCount = await buttons.count();

      for (let i = 0; i < buttonCount; i++) {
        const button = buttons.nth(i);
        const ariaLabel = await button.getAttribute('aria-label');
        const textContent = await button.textContent();
        
        // Button should have accessible name
        expect(ariaLabel || textContent?.trim()).toBeTruthy();
      }
    });

    await test.step('Check form field descriptions', async () => {
      // Navigate to contact form
      await page.click('[data-testid="event-type-engagement"]');
      await page.click('[data-testid="next-step"]');
      await page.click('[data-testid="package-basic-engagement"]');
      await page.click('[data-testid="next-step"]');
      await page.fill('[data-testid="event-date"]', '2025-12-25');
      await page.click('[data-testid="next-step"]');

      const inputs = page.locator('input, select, textarea');
      const inputCount = await inputs.count();

      for (let i = 0; i < inputCount; i++) {
        const input = inputs.nth(i);
        const ariaDescribedBy = await input.getAttribute('aria-describedby');
        
        if (ariaDescribedBy) {
          const description = page.locator(`#${ariaDescribedBy}`);
          await expect(description).toBeVisible();
        }
      }
    });
  });

  test('should announce form validation errors @desktop', async ({ page }) => {
    await page.goto('/');
    
    // Navigate to contact form
    await page.click('[data-testid="event-type-engagement"]');
    await page.click('[data-testid="next-step"]');
    await page.click('[data-testid="package-basic-engagement"]');
    await page.click('[data-testid="next-step"]');
    await page.fill('[data-testid="event-date"]', '2025-12-25');
    await page.click('[data-testid="next-step"]');

    await test.step('Test error announcements', async () => {
      // Submit form without required fields
      await page.click('[data-testid="submit-booking"]');
      
      // Error messages should have proper ARIA attributes
      const errorMessages = page.locator('[data-testid*="error"]');
      const errorCount = await errorMessages.count();

      for (let i = 0; i < errorCount; i++) {
        const error = errorMessages.nth(i);
        const ariaLive = await error.getAttribute('aria-live');
        const role = await error.getAttribute('role');
        
        // Error should be announced to screen readers
        expect(ariaLive === 'polite' || ariaLive === 'assertive' || role === 'alert').toBeTruthy();
      }
    });
  });

  test('should support screen reader navigation @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Check heading navigation', async () => {
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      const headingCount = await headings.count();

      expect(headingCount).toBeGreaterThan(0);

      // Each heading should have meaningful text
      for (let i = 0; i < headingCount; i++) {
        const heading = headings.nth(i);
        const text = await heading.textContent();
        expect(text?.trim().length).toBeGreaterThan(0);
      }
    });

    await test.step('Check list structure', async () => {
      const lists = page.locator('ul, ol');
      const listCount = await lists.count();

      for (let i = 0; i < listCount; i++) {
        const list = lists.nth(i);
        const listItems = list.locator('li');
        const itemCount = await listItems.count();
        
        // Lists should have list items
        expect(itemCount).toBeGreaterThan(0);
      }
    });
  });

  test('should handle dynamic content updates @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Test live region updates', async () => {
      // Navigate through form to trigger dynamic updates
      await page.click('[data-testid="event-type-engagement"]');
      
      // Check if status updates are announced
      const statusRegion = page.locator('[aria-live], [role="status"]');
      if (await statusRegion.count() > 0) {
        const ariaLive = await statusRegion.first().getAttribute('aria-live');
        expect(['polite', 'assertive']).toContain(ariaLive);
      }
    });
  });
});
