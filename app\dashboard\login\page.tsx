import { AuthProvider } from '@/lib/auth/auth-context';
import { AuthGuard } from '@/lib/auth/auth-guard';
import { LoginForm } from '@/components/auth/login-form';

export default function LoginPage() {
  return (
    <AuthProvider>
      <AuthGuard requireAuth={false}>
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-rose-50 via-white to-amber-50">
          <LoginForm />
        </div>
      </AuthGuard>
    </AuthProvider>
  );
}
