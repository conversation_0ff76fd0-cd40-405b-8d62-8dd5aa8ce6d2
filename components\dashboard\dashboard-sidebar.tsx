'use client';

import { useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { 
  LayoutDashboard, 
  Calendar, 
  Users, 
  BarChart3, 
  Settings, 
  Camera,
  Bell,
  LogOut,
  ChevronRight
} from 'lucide-react';
import { useAuth } from '@/lib/auth/auth-context';
import { useDashboardStore } from '@/lib/store/dashboard-store';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

interface DashboardSidebarProps {
  collapsed: boolean;
}

const navigationItems = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    description: 'Overview and quick stats'
  },
  {
    name: 'Bookings',
    href: '/dashboard/bookings',
    icon: Calendar,
    description: 'Manage your bookings'
  },
  {
    name: 'Clients',
    href: '/dashboard/clients',
    icon: Users,
    description: 'Client management'
  },
  {
    name: 'Analytics',
    href: '/dashboard/analytics',
    icon: BarChart3,
    description: 'Business insights'
  },
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
    description: 'Account and preferences'
  }
];

export function DashboardSidebar({ collapsed }: DashboardSidebarProps) {
  const pathname = usePathname();
  const { profile, signOut } = useAuth();
  const { ui } = useDashboardStore();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);

  // GSAP animations for premium feel
  useEffect(() => {
    if (sidebarRef.current) {
      gsap.fromTo(
        sidebarRef.current.children,
        { opacity: 0, x: -20 },
        { 
          opacity: 1, 
          x: 0, 
          duration: 0.6, 
          stagger: 0.1,
          ease: 'power2.out'
        }
      );
    }
  }, []);

  // Logo animation
  useEffect(() => {
    if (logoRef.current) {
      gsap.to(logoRef.current, {
        rotation: 360,
        duration: 2,
        ease: 'power2.inOut',
        repeat: -1,
        repeatDelay: 5
      });
    }
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const unreadNotifications = ui.notifications.filter(n => !n.read).length;

  return (
    <div 
      ref={sidebarRef}
      className="h-full flex flex-col bg-white/5 backdrop-blur-xl border-r border-white/10"
    >
      {/* Logo and Brand */}
      <div className="p-6 border-b border-white/10">
        <Link href="/dashboard" className="flex items-center gap-3">
          <div 
            ref={logoRef}
            className="w-8 h-8 bg-gradient-to-br from-rose-500 to-amber-500 rounded-lg flex items-center justify-center"
          >
            <Camera className="w-5 h-5 text-white" />
          </div>
          {!collapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <h1 className="text-lg font-bold text-gray-900">
                {profile?.business_name || 'Photography Studio'}
              </h1>
              <p className="text-xs text-gray-600">Professional Dashboard</p>
            </motion.div>
          )}
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item, index) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;
          
          return (
            <motion.div
              key={item.href}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Link href={item.href}>
                <div
                  className={cn(
                    'group relative flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all duration-200',
                    'hover:bg-white/10 hover:backdrop-blur-sm',
                    isActive && 'bg-white/15 backdrop-blur-sm border border-white/20 shadow-lg'
                  )}
                >
                  {/* Active indicator */}
                  {isActive && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute inset-0 bg-gradient-to-r from-rose-500/10 to-amber-500/10 rounded-lg"
                      transition={{ type: 'spring', bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                  
                  <Icon 
                    className={cn(
                      'w-5 h-5 transition-colors duration-200',
                      isActive ? 'text-rose-600' : 'text-gray-600 group-hover:text-gray-900'
                    )} 
                  />
                  
                  {!collapsed && (
                    <>
                      <div className="flex-1">
                        <p className={cn(
                          'font-medium transition-colors duration-200',
                          isActive ? 'text-gray-900' : 'text-gray-700 group-hover:text-gray-900'
                        )}>
                          {item.name}
                        </p>
                        <p className="text-xs text-gray-500 group-hover:text-gray-600">
                          {item.description}
                        </p>
                      </div>
                      
                      {item.name === 'Bookings' && unreadNotifications > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {unreadNotifications}
                        </Badge>
                      )}
                      
                      <ChevronRight 
                        className={cn(
                          'w-4 h-4 transition-all duration-200',
                          isActive ? 'text-rose-600 rotate-90' : 'text-gray-400 group-hover:text-gray-600'
                        )}
                      />
                    </>
                  )}
                </div>
              </Link>
            </motion.div>
          );
        })}
      </nav>

      {/* User Profile and Actions */}
      <div className="p-4 border-t border-white/10 space-y-3">
        {/* Notifications */}
        {!collapsed && unreadNotifications > 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="glass p-3 rounded-lg border border-amber-200/50 bg-amber-50/80"
          >
            <div className="flex items-center gap-2">
              <Bell className="w-4 h-4 text-amber-600" />
              <span className="text-sm font-medium text-amber-800">
                {unreadNotifications} new notification{unreadNotifications !== 1 ? 's' : ''}
              </span>
            </div>
          </motion.div>
        )}

        {/* User Profile */}
        <div className="flex items-center gap-3">
          <Avatar className="w-10 h-10 border-2 border-white/20">
            <AvatarImage src={profile?.logo_url} />
            <AvatarFallback className="bg-gradient-to-br from-rose-500 to-amber-500 text-white font-semibold">
              {profile?.owner_name?.charAt(0) || 'P'}
            </AvatarFallback>
          </Avatar>
          
          {!collapsed && (
            <div className="flex-1 min-w-0">
              <p className="font-medium text-gray-900 truncate">
                {profile?.owner_name || 'Photographer'}
              </p>
              <p className="text-xs text-gray-600 truncate">
                {profile?.email}
              </p>
            </div>
          )}
        </div>

        {/* Sign Out Button */}
        <Button
          onClick={handleSignOut}
          variant="ghost"
          size={collapsed ? "icon" : "sm"}
          className="w-full justify-start text-gray-600 hover:text-gray-900 hover:bg-white/10"
        >
          <LogOut className="w-4 h-4" />
          {!collapsed && <span className="ml-2">Sign Out</span>}
        </Button>
      </div>
    </div>
  );
}
