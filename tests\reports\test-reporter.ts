import { Reporter, TestCase, TestResult, FullResult } from '@playwright/test/reporter';
import fs from 'fs';
import path from 'path';

/**
 * Custom Test Reporter for Tera Works Photographer Booking System
 * Generates comprehensive test reports with performance metrics and recommendations
 */

interface TestMetrics {
  totalTests: number;
  passed: number;
  failed: number;
  skipped: number;
  flaky: number;
  duration: number;
  coverage: {
    booking: number;
    dashboard: number;
    integration: number;
    audit: number;
  };
  performance: {
    averagePageLoad: number;
    averageFormSubmission: number;
    slowestTest: { name: string; duration: number };
    fastestTest: { name: string; duration: number };
  };
  accessibility: {
    violations: number;
    warnings: number;
    passes: number;
  };
  security: {
    vulnerabilities: number;
    warnings: number;
    passes: number;
  };
}

class TeraWorksTestReporter implements Reporter {
  private startTime: number = 0;
  private results: TestResult[] = [];
  private metrics: TestMetrics = {
    totalTests: 0,
    passed: 0,
    failed: 0,
    skipped: 0,
    flaky: 0,
    duration: 0,
    coverage: { booking: 0, dashboard: 0, integration: 0, audit: 0 },
    performance: {
      averagePageLoad: 0,
      averageFormSubmission: 0,
      slowestTest: { name: '', duration: 0 },
      fastestTest: { name: '', duration: Infinity }
    },
    accessibility: { violations: 0, warnings: 0, passes: 0 },
    security: { vulnerabilities: 0, warnings: 0, passes: 0 }
  };

  onBegin() {
    this.startTime = Date.now();
    console.log('🚀 Starting Tera Works Photographer Booking System Test Suite');
    console.log('📊 Generating comprehensive test report...\n');
  }

  onTestEnd(test: TestCase, result: TestResult) {
    this.results.push(result);
    this.updateMetrics(test, result);
  }

  onEnd(result: FullResult) {
    this.metrics.duration = Date.now() - this.startTime;
    this.generateReports();
    this.printSummary();
  }

  private updateMetrics(test: TestCase, result: TestResult) {
    this.metrics.totalTests++;

    switch (result.status) {
      case 'passed':
        this.metrics.passed++;
        break;
      case 'failed':
        this.metrics.failed++;
        break;
      case 'skipped':
        this.metrics.skipped++;
        break;
      case 'timedOut':
        this.metrics.failed++;
        break;
    }

    // Update performance metrics
    if (result.duration > this.metrics.performance.slowestTest.duration) {
      this.metrics.performance.slowestTest = {
        name: test.title,
        duration: result.duration
      };
    }

    if (result.duration < this.metrics.performance.fastestTest.duration) {
      this.metrics.performance.fastestTest = {
        name: test.title,
        duration: result.duration
      };
    }

    // Update coverage by test category
    const testFile = test.location.file;
    if (testFile.includes('/booking/')) {
      this.metrics.coverage.booking++;
    } else if (testFile.includes('/dashboard/')) {
      this.metrics.coverage.dashboard++;
    } else if (testFile.includes('/integration/')) {
      this.metrics.coverage.integration++;
    } else if (testFile.includes('/audit/')) {
      this.metrics.coverage.audit++;
    }

    // Extract accessibility and security metrics from test annotations
    test.annotations.forEach(annotation => {
      if (annotation.type === 'accessibility') {
        const data = JSON.parse(annotation.description || '{}');
        this.metrics.accessibility.violations += data.violations || 0;
        this.metrics.accessibility.warnings += data.warnings || 0;
        this.metrics.accessibility.passes += data.passes || 0;
      }
      
      if (annotation.type === 'security') {
        const data = JSON.parse(annotation.description || '{}');
        this.metrics.security.vulnerabilities += data.vulnerabilities || 0;
        this.metrics.security.warnings += data.warnings || 0;
        this.metrics.security.passes += data.passes || 0;
      }
    });
  }

  private generateReports() {
    const reportDir = 'test-results/reports';
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    // Generate JSON report
    this.generateJSONReport(reportDir);
    
    // Generate HTML report
    this.generateHTMLReport(reportDir);
    
    // Generate CSV report for metrics
    this.generateCSVReport(reportDir);
    
    // Generate recommendations
    this.generateRecommendations(reportDir);
  }

  private generateJSONReport(reportDir: string) {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: this.metrics,
      results: this.results.map(result => ({
        title: (result as any).test?.title || 'Unknown Test',
        status: result.status,
        duration: result.duration,
        errors: result.errors,
        attachments: result.attachments?.map(a => a.name)
      })),
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        ci: !!process.env.CI
      }
    };

    fs.writeFileSync(
      path.join(reportDir, 'test-report.json'),
      JSON.stringify(report, null, 2)
    );
  }

  private generateHTMLReport(reportDir: string) {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tera Works Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .header { text-align: center; margin-bottom: 30px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .metric-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .metric-label { color: #666; margin-top: 5px; }
        .status-passed { color: #28a745; }
        .status-failed { color: #dc3545; }
        .status-skipped { color: #ffc107; }
        .recommendations { background: #e7f3ff; padding: 20px; border-radius: 8px; margin-top: 20px; }
        .chart { margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Tera Works Photographer Booking System</h1>
            <h2>Test Execution Report</h2>
            <p>Generated on ${new Date().toLocaleString()}</p>
        </div>

        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value status-passed">${this.metrics.passed}</div>
                <div class="metric-label">Tests Passed</div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-failed">${this.metrics.failed}</div>
                <div class="metric-label">Tests Failed</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${Math.round(this.metrics.duration / 1000)}s</div>
                <div class="metric-label">Total Duration</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${Math.round((this.metrics.passed / this.metrics.totalTests) * 100)}%</div>
                <div class="metric-label">Success Rate</div>
            </div>
        </div>

        <h3>📊 Test Coverage by Category</h3>
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value">${this.metrics.coverage.booking}</div>
                <div class="metric-label">Booking Tests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${this.metrics.coverage.dashboard}</div>
                <div class="metric-label">Dashboard Tests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${this.metrics.coverage.integration}</div>
                <div class="metric-label">Integration Tests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${this.metrics.coverage.audit}</div>
                <div class="metric-label">Audit Tests</div>
            </div>
        </div>

        <h3>⚡ Performance Metrics</h3>
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value">${Math.round(this.metrics.performance.slowestTest.duration)}ms</div>
                <div class="metric-label">Slowest Test: ${this.metrics.performance.slowestTest.name}</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${Math.round(this.metrics.performance.fastestTest.duration)}ms</div>
                <div class="metric-label">Fastest Test: ${this.metrics.performance.fastestTest.name}</div>
            </div>
        </div>

        <h3>♿ Accessibility Results</h3>
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value status-failed">${this.metrics.accessibility.violations}</div>
                <div class="metric-label">Violations</div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-skipped">${this.metrics.accessibility.warnings}</div>
                <div class="metric-label">Warnings</div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-passed">${this.metrics.accessibility.passes}</div>
                <div class="metric-label">Passes</div>
            </div>
        </div>

        <h3>🔒 Security Results</h3>
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value status-failed">${this.metrics.security.vulnerabilities}</div>
                <div class="metric-label">Vulnerabilities</div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-skipped">${this.metrics.security.warnings}</div>
                <div class="metric-label">Warnings</div>
            </div>
            <div class="metric-card">
                <div class="metric-value status-passed">${this.metrics.security.passes}</div>
                <div class="metric-label">Passes</div>
            </div>
        </div>

        ${this.generateRecommendationsHTML()}
    </div>
</body>
</html>`;

    fs.writeFileSync(path.join(reportDir, 'test-report.html'), html);
  }

  private generateCSVReport(reportDir: string) {
    const csvData = [
      ['Metric', 'Value'],
      ['Total Tests', this.metrics.totalTests.toString()],
      ['Passed', this.metrics.passed.toString()],
      ['Failed', this.metrics.failed.toString()],
      ['Skipped', this.metrics.skipped.toString()],
      ['Success Rate', `${Math.round((this.metrics.passed / this.metrics.totalTests) * 100)}%`],
      ['Duration (seconds)', Math.round(this.metrics.duration / 1000).toString()],
      ['Booking Tests', this.metrics.coverage.booking.toString()],
      ['Dashboard Tests', this.metrics.coverage.dashboard.toString()],
      ['Integration Tests', this.metrics.coverage.integration.toString()],
      ['Audit Tests', this.metrics.coverage.audit.toString()],
      ['Accessibility Violations', this.metrics.accessibility.violations.toString()],
      ['Security Vulnerabilities', this.metrics.security.vulnerabilities.toString()]
    ];

    const csv = csvData.map(row => row.join(',')).join('\n');
    fs.writeFileSync(path.join(reportDir, 'test-metrics.csv'), csv);
  }

  private generateRecommendations(reportDir: string) {
    const recommendations = this.getRecommendations();
    fs.writeFileSync(
      path.join(reportDir, 'recommendations.md'),
      recommendations
    );
  }

  private generateRecommendationsHTML(): string {
    const recommendations = this.getRecommendations();
    return `
        <div class="recommendations">
            <h3>💡 Recommendations</h3>
            <pre>${recommendations}</pre>
        </div>
    `;
  }

  private getRecommendations(): string {
    const recommendations: string[] = [];
    const successRate = (this.metrics.passed / this.metrics.totalTests) * 100;

    recommendations.push('# Test Execution Recommendations\n');

    if (successRate < 95) {
      recommendations.push('⚠️ **Test Success Rate Below 95%**');
      recommendations.push('- Review failed tests and fix underlying issues');
      recommendations.push('- Consider improving test stability and reliability\n');
    }

    if (this.metrics.accessibility.violations > 0) {
      recommendations.push('♿ **Accessibility Issues Detected**');
      recommendations.push('- Fix accessibility violations to ensure WCAG compliance');
      recommendations.push('- Review form labels, color contrast, and keyboard navigation\n');
    }

    if (this.metrics.security.vulnerabilities > 0) {
      recommendations.push('🔒 **Security Vulnerabilities Found**');
      recommendations.push('- Address security vulnerabilities immediately');
      recommendations.push('- Review input validation and authentication mechanisms\n');
    }

    if (this.metrics.performance.slowestTest.duration > 30000) {
      recommendations.push('⚡ **Performance Optimization Needed**');
      recommendations.push(`- Slowest test: ${this.metrics.performance.slowestTest.name} (${Math.round(this.metrics.performance.slowestTest.duration)}ms)`);
      recommendations.push('- Consider optimizing page load times and form submissions\n');
    }

    if (this.metrics.coverage.booking < 10) {
      recommendations.push('📋 **Increase Booking Test Coverage**');
      recommendations.push('- Add more comprehensive booking form tests');
      recommendations.push('- Test all event types and edge cases\n');
    }

    if (recommendations.length === 1) {
      recommendations.push('✅ **All Tests Performing Well**');
      recommendations.push('- Maintain current test quality and coverage');
      recommendations.push('- Consider adding more edge case testing\n');
    }

    return recommendations.join('\n');
  }

  private printSummary() {
    const successRate = Math.round((this.metrics.passed / this.metrics.totalTests) * 100);
    
    console.log('\n📊 TEST EXECUTION SUMMARY');
    console.log('═'.repeat(50));
    console.log(`✅ Passed: ${this.metrics.passed}`);
    console.log(`❌ Failed: ${this.metrics.failed}`);
    console.log(`⏭️  Skipped: ${this.metrics.skipped}`);
    console.log(`📈 Success Rate: ${successRate}%`);
    console.log(`⏱️  Duration: ${Math.round(this.metrics.duration / 1000)}s`);
    console.log('═'.repeat(50));
    
    if (this.metrics.failed > 0) {
      console.log('❌ Some tests failed. Check the detailed report for more information.');
    } else {
      console.log('🎉 All tests passed successfully!');
    }
    
    console.log('\n📁 Reports generated in test-results/reports/');
    console.log('   - test-report.html (Detailed HTML report)');
    console.log('   - test-report.json (Machine-readable data)');
    console.log('   - test-metrics.csv (Metrics for analysis)');
    console.log('   - recommendations.md (Improvement suggestions)');
  }
}

export default TeraWorksTestReporter;
