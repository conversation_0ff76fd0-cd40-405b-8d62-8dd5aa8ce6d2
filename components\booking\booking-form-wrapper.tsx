'use client';

import { useEffect, useState } from 'react';
import { Booking } from '@/components/Booking';
import { supabase } from '@/lib/supabase/client';
import { Database } from '@/lib/supabase/types';

type BookingInsert = Database['public']['Tables']['bookings']['Insert'];

// Enhanced booking form that integrates with Supabase while preserving existing functionality
export function BookingFormWrapper() {
  const [isSupabaseConnected, setIsSupabaseConnected] = useState(false);

  // Check Supabase connection on mount
  useEffect(() => {
    const checkConnection = async () => {
      try {
        const { data, error } = await supabase.from('photographer_profile').select('id').limit(1);
        setIsSupabaseConnected(!error);
      } catch (error) {
        console.log('Supabase not connected, using fallback mode');
        setIsSupabaseConnected(false);
      }
    };

    checkConnection();
  }, []);

  // Enhanced form submission handler
  const handleEnhancedSubmission = async (formData: any) => {
    console.log('Enhanced booking submission:', formData);

    // Always try to save to Supabase if connected
    if (isSupabaseConnected) {
      try {
        // Generate booking reference
        const { data: reference } = await supabase.rpc('generate_booking_reference');
        
        // Transform form data to match database schema
        const bookingData: BookingInsert = {
          booking_reference: reference || `BKG-${Date.now()}`,
          client_name: formData.name,
          client_email: formData.email,
          client_phone: formData.phone,
          event_type: formData.eventType,
          event_date: formData.eventDate ? new Date(formData.eventDate).toISOString().split('T')[0] : null,
          venue: formData.venue,
          package_type: formData.package,
          form_data: formData, // Store complete form data as JSON
          status: 'pending',
          email_sent: false,
          whatsapp_sent: false,
        };

        // Save to database
        const { data: booking, error } = await supabase
          .from('bookings')
          .insert(bookingData)
          .select()
          .single();

        if (error) {
          console.error('Error saving to database:', error);
          // Continue with original flow even if database save fails
        } else {
          console.log('Booking saved to database:', booking);
          
          // Log analytics event
          await supabase.rpc('log_analytics_event', {
            p_event_type: 'booking_submitted',
            p_event_data: { 
              event_type: formData.eventType,
              package: formData.package,
              source: 'booking_form'
            },
            p_booking_id: booking.id,
          });

          // Update form data with booking reference for EmailJS
          formData.bookingReference = booking.booking_reference;
        }
      } catch (error) {
        console.error('Database operation failed:', error);
        // Continue with original EmailJS flow
      }
    }

    // Return enhanced form data (with booking reference if available)
    return formData;
  };

  return (
    <div className="min-h-screen">
      {/* Existing booking form with enhanced functionality */}
      <Booking onEnhancedSubmit={handleEnhancedSubmission} />
      
      {/* Hidden admin access button - only visible to photographers */}
      <AdminAccessButton />
    </div>
  );
}

// Hidden admin access button for photographers
function AdminAccessButton() {
  const [showAdminAccess, setShowAdminAccess] = useState(false);

  // Check if user is authenticated (photographer)
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setShowAdminAccess(!!session);
    };

    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setShowAdminAccess(!!session);
    });

    return () => subscription.unsubscribe();
  }, []);

  if (!showAdminAccess) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <a
        href="/dashboard"
        className="inline-flex items-center px-4 py-2 bg-black/10 backdrop-blur-md border border-white/20 rounded-lg text-sm font-medium text-gray-700 hover:bg-black/20 transition-all duration-200 shadow-lg"
      >
        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        Dashboard
      </a>
    </div>
  );
}
