import { test, expect } from '@playwright/test';
import { BookingFormPage, generateBookingData, generateInvalidBookingData, interceptEmailJS, interceptSupabase } from '../utils/test-helpers';

/**
 * End-to-End Booking Form Tests
 * Tests the complete booking flow for all event types
 */

test.describe('Booking Form - Complete Flow', () => {
  let bookingPage: BookingFormPage;

  test.beforeEach(async ({ page }) => {
    bookingPage = new BookingFormPage(page);
    await bookingPage.goto();
  });

  test('should complete engagement booking successfully @desktop', async ({ page }) => {
    const bookingData = generateBookingData('engagement');
    const emailInterceptor = await interceptEmailJS(page);
    const supabaseInterceptor = await interceptSupabase(page);

    // Step 1: Select event type
    await test.step('Select engagement event type', async () => {
      await expect(page.locator('[data-testid="event-type-engagement"]')).toBeVisible();
      await bookingPage.fillStep1('engagement');
    });

    // Step 2: Select package
    await test.step('Select engagement package', async () => {
      await expect(page.locator('[data-testid="package-selection"]')).toBeVisible();
      await bookingPage.fillStep2(bookingData.package);
    });

    // Step 3: Fill event details
    await test.step('Fill engagement event details', async () => {
      await expect(page.locator('[data-testid="event-details"]')).toBeVisible();
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
      await page.selectOption('[data-testid="outfit-changes"]', bookingData.outfitChanges);
      await page.click('[data-testid="next-step"]');
    });

    // Step 4: Fill contact information
    await test.step('Fill contact information', async () => {
      await expect(page.locator('[data-testid="contact-form"]')).toBeVisible();
      await bookingPage.fillStep4(bookingData);
    });

    // Submit form
    await test.step('Submit booking form', async () => {
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    // Verify success
    await test.step('Verify booking success', async () => {
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Booking submitted successfully');
      await expect(page.locator('[data-testid="booking-reference"]')).toBeVisible();
    });

    // Verify EmailJS integration
    await test.step('Verify email notification sent', async () => {
      expect(emailInterceptor.wasEmailSent()).toBe(true);
      const emailData = emailInterceptor.getEmailData();
      expect(emailData.client_name).toBe(bookingData.brideAName);
      expect(emailData.event_type).toBe('engagement');
      expect(emailData.is_engagement).toBe(true);
    });

    // Verify database integration
    await test.step('Verify database storage', async () => {
      const requests = supabaseInterceptor.getRequests();
      const bookingInsert = requests.find(r => r.url.includes('/bookings') && r.method === 'POST');
      expect(bookingInsert).toBeDefined();
      expect(bookingInsert.data.client_name).toBe(bookingData.brideAName);
      expect(bookingInsert.data.event_type).toBe('engagement');
    });
  });

  test('should complete wedding booking successfully @desktop', async ({ page }) => {
    const bookingData = generateBookingData('wedding');
    const emailInterceptor = await interceptEmailJS(page);

    await test.step('Complete wedding booking flow', async () => {
      // Step 1: Select wedding event type
      await bookingPage.fillStep1('wedding');

      // Step 2: Select wedding package
      await bookingPage.fillStep2(bookingData.package);

      // Step 3: Fill wedding details
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="registration-time"]', bookingData.registrationTime);
      await page.fill('[data-testid="ceremony-time"]', bookingData.ceremonyTime);
      await page.fill('[data-testid="event-end-time"]', bookingData.eventEndTime);
      await page.fill('[data-testid="guest-count"]', bookingData.guestCount);
      await page.fill('[data-testid="makeup-artist"]', bookingData.makeupArtist);
      await page.click('[data-testid="next-step"]');

      // Step 4: Fill contact information
      await bookingPage.fillStep4(bookingData);

      // Submit and verify
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify wedding-specific data', async () => {
      expect(emailInterceptor.wasEmailSent()).toBe(true);
      const emailData = emailInterceptor.getEmailData();
      expect(emailData.event_type).toBe('wedding');
      expect(emailData.is_wedding).toBe(true);
      expect(emailData.venue_location).toBe(bookingData.venue);
    });
  });

  test('should complete homecoming booking successfully @desktop', async ({ page }) => {
    const bookingData = generateBookingData('homecoming');

    await test.step('Complete homecoming booking flow', async () => {
      await bookingPage.fillStep1('homecoming');
      await bookingPage.fillStep2(bookingData.package);

      // Fill homecoming-specific details
      await page.fill('[data-testid="homecoming-date"]', bookingData.homecomingDate);
      await page.fill('[data-testid="homecoming-venue"]', bookingData.homecomingVenue);
      await page.fill('[data-testid="homecoming-end-time"]', bookingData.homecomingEndTime);
      await page.click('[data-testid="next-step"]');

      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify homecoming success', async () => {
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="calendar-link"]')).toBeVisible();
    });
  });

  test('should complete dual combo (wedding + homecoming) booking @desktop', async ({ page }) => {
    const bookingData = generateBookingData('wedding-homecoming');
    const emailInterceptor = await interceptEmailJS(page);

    await test.step('Complete dual combo booking flow', async () => {
      await bookingPage.fillStep1('wedding-homecoming');
      await bookingPage.fillStep2(bookingData.package);

      // Fill dual event details
      await page.fill('[data-testid="wedding-date"]', bookingData.weddingDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="homecoming-date"]', bookingData.homecomingDate);
      await page.fill('[data-testid="homecoming-venue"]', bookingData.homecomingVenue);
      await page.fill('[data-testid="registration-time"]', bookingData.registrationTime);
      await page.fill('[data-testid="ceremony-time"]', bookingData.ceremonyTime);
      await page.fill('[data-testid="event-end-time"]', bookingData.eventEndTime);
      await page.fill('[data-testid="homecoming-end-time"]', bookingData.homecomingEndTime);
      await page.fill('[data-testid="guest-count"]', bookingData.guestCount);
      await page.click('[data-testid="next-step"]');

      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify dual combo specific features', async () => {
      expect(emailInterceptor.wasEmailSent()).toBe(true);
      const emailData = emailInterceptor.getEmailData();
      expect(emailData.is_combo).toBe(true);
      expect(emailData.calendar_link_wedding).toBeDefined();
      expect(emailData.calendar_link_homecoming).toBeDefined();
    });
  });

  test('should complete triple combo booking successfully @desktop', async ({ page }) => {
    const bookingData = generateBookingData('triple-combo');

    await test.step('Complete triple combo booking flow', async () => {
      await bookingPage.fillStep1('triple-combo');
      await bookingPage.fillStep2(bookingData.package);

      // Fill all three event details
      await page.fill('[data-testid="event-date"]', bookingData.eventDate); // Engagement
      await page.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
      await page.selectOption('[data-testid="outfit-changes"]', bookingData.outfitChanges);
      
      await page.fill('[data-testid="wedding-date"]', bookingData.weddingDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="registration-time"]', bookingData.registrationTime);
      await page.fill('[data-testid="ceremony-time"]', bookingData.ceremonyTime);
      await page.fill('[data-testid="event-end-time"]', bookingData.eventEndTime);
      
      await page.fill('[data-testid="homecoming-date"]', bookingData.homecomingDate);
      await page.fill('[data-testid="homecoming-venue"]', bookingData.homecomingVenue);
      await page.fill('[data-testid="homecoming-end-time"]', bookingData.homecomingEndTime);
      await page.fill('[data-testid="guest-count"]', bookingData.guestCount);
      await page.click('[data-testid="next-step"]');

      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify triple combo features', async () => {
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Triple combo booking');
      await expect(page.locator('[data-testid="calendar-link-engagement"]')).toBeVisible();
      await expect(page.locator('[data-testid="calendar-link-wedding"]')).toBeVisible();
      await expect(page.locator('[data-testid="calendar-link-homecoming"]')).toBeVisible();
    });
  });
});

test.describe('Booking Form - Validation Tests', () => {
  let bookingPage: BookingFormPage;

  test.beforeEach(async ({ page }) => {
    bookingPage = new BookingFormPage(page);
    await bookingPage.goto();
  });

  test('should validate required fields @desktop', async ({ page }) => {
    const invalidData = generateInvalidBookingData();

    await test.step('Navigate to contact form', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2('Basic Engagement');
      await page.fill('[data-testid="event-date"]', '2025-12-25');
      await page.click('[data-testid="next-step"]');
    });

    await test.step('Submit form with empty required fields', async () => {
      // Leave required fields empty
      await page.click('[data-testid="submit-booking"]');
    });

    await test.step('Verify validation errors', async () => {
      const errors = await bookingPage.getValidationErrors();
      expect(errors).toContain('Name is required');
      expect(errors).toContain('Email is required');
      expect(errors).toContain('Phone number is required');
    });
  });

  test('should validate email format @desktop', async ({ page }) => {
    const invalidData = generateInvalidBookingData();

    await test.step('Fill form with invalid email', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2('Basic Engagement');
      await page.fill('[data-testid="event-date"]', '2025-12-25');
      await page.click('[data-testid="next-step"]');

      await page.fill('[data-testid="bride-name"]', 'Test Bride');
      await page.fill('[data-testid="email"]', invalidData.invalidEmail.email);
      await page.fill('[data-testid="phone"]', '+94771234567');
      await page.click('[data-testid="submit-booking"]');
    });

    await test.step('Verify email validation error', async () => {
      await expect(page.locator('[data-testid="email-error"]')).toContainText('Invalid email format');
    });
  });

  test('should validate phone number format @desktop', async ({ page }) => {
    const invalidData = generateInvalidBookingData();

    await test.step('Fill form with invalid phone', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2('Basic Engagement');
      await page.fill('[data-testid="event-date"]', '2025-12-25');
      await page.click('[data-testid="next-step"]');

      await page.fill('[data-testid="bride-name"]', 'Test Bride');
      await page.fill('[data-testid="email"]', '<EMAIL>');
      await page.fill('[data-testid="phone"]', invalidData.invalidPhone.phoneNumber);
      await page.click('[data-testid="submit-booking"]');
    });

    await test.step('Verify phone validation error', async () => {
      await expect(page.locator('[data-testid="phone-error"]')).toContainText('Invalid phone number format');
    });
  });

  test('should validate future date requirement @desktop', async ({ page }) => {
    const invalidData = generateInvalidBookingData();

    await test.step('Fill form with past date', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2('Basic Engagement');
      await page.fill('[data-testid="event-date"]', invalidData.pastDate.eventDate);
      await page.click('[data-testid="next-step"]');
    });

    await test.step('Verify date validation error', async () => {
      await expect(page.locator('[data-testid="date-error"]')).toContainText('Event date must be in the future');
    });
  });

  test('should require terms agreement @desktop', async ({ page }) => {
    const bookingData = generateBookingData('engagement');

    await test.step('Fill complete form without agreeing to terms', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2(bookingData.package);
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.click('[data-testid="next-step"]');

      await page.fill('[data-testid="bride-name"]', bookingData.brideAName);
      await page.fill('[data-testid="email"]', bookingData.email);
      await page.fill('[data-testid="phone"]', bookingData.phoneNumber);
      // Don't check terms agreement
      await page.click('[data-testid="submit-booking"]');
    });

    await test.step('Verify terms validation error', async () => {
      await expect(page.locator('[data-testid="terms-error"]')).toContainText('You must agree to the terms');
    });
  });
});
