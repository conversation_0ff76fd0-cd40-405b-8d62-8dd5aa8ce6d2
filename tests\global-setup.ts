import { chromium, FullConfig } from '@playwright/test';
import { createTestPhotographer, cleanupTestData } from './utils/test-helpers';

/**
 * Global Setup for Tera Works Photographer Booking System Tests
 * Runs once before all tests to prepare the test environment
 */
async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup...');

  // Clean up any existing test data
  await cleanupTestData();

  // Create test photographer account
  const testPhotographer = await createTestPhotographer();
  console.log(`📸 Created test photographer: ${testPhotographer.email}`);

  // Set up authentication state for dashboard tests
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Navigate to login page
    await page.goto(process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000/dashboard/login');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Check if we need to create the photographer account first
    const signUpLink = page.locator('text=Sign up');
    if (await signUpLink.isVisible()) {
      console.log('📝 Creating photographer account...');
      
      await signUpLink.click();
      await page.fill('[data-testid="email"]', testPhotographer.email);
      await page.fill('[data-testid="password"]', testPhotographer.password);
      await page.fill('[data-testid="business-name"]', testPhotographer.profile.business_name);
      await page.fill('[data-testid="owner-name"]', testPhotographer.profile.owner_name);
      await page.click('[data-testid="signup-button"]');
      
      // Wait for account creation and potential email verification
      await page.waitForTimeout(5000);
    }

    // Login with test photographer credentials
    console.log('🔐 Logging in test photographer...');
    await page.fill('[data-testid="email"]', testPhotographer.email);
    await page.fill('[data-testid="password"]', testPhotographer.password);
    await page.click('[data-testid="login-button"]');

    // Wait for successful login
    await page.waitForURL('**/dashboard', { timeout: 30000 });
    console.log('✅ Test photographer logged in successfully');

    // Save authentication state
    await page.context().storageState({ path: 'tests/auth/photographer.json' });
    console.log('💾 Authentication state saved');

  } catch (error) {
    console.error('❌ Failed to set up authentication:', error);
    throw error;
  } finally {
    await browser.close();
  }

  // Set up test environment variables
  process.env.TEST_PHOTOGRAPHER_EMAIL = testPhotographer.email;
  process.env.TEST_PHOTOGRAPHER_PASSWORD = testPhotographer.password;

  console.log('✅ Global setup completed successfully');
}

export default globalSetup;
