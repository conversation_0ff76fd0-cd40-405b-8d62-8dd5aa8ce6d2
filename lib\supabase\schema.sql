-- Standalone Photographer Dashboard Database Schema
-- This schema is designed for individual photographer use (not multi-tenant)

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Photographer profile table (single photographer per database)
CREATE TABLE photographer_profile (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  business_name TEXT NOT NULL,
  owner_name TEXT NOT NULL,
  phone TEXT,
  whatsapp_number TEXT,
  logo_url TEXT,
  website_url TEXT,
  address TEXT,
  bio TEXT,
  
  -- EmailJS Configuration
  emailjs_service_id TEXT,
  emailjs_template_id TEXT,
  emailjs_public_key TEXT,
  
  -- Business Settings
  timezone TEXT DEFAULT 'Asia/Colombo',
  currency TEXT DEFAULT 'LKR',
  business_hours JSONB DEFAULT '{"monday": {"open": "09:00", "close": "17:00", "enabled": true}, "tuesday": {"open": "09:00", "close": "17:00", "enabled": true}, "wednesday": {"open": "09:00", "close": "17:00", "enabled": true}, "thursday": {"open": "09:00", "close": "17:00", "enabled": true}, "friday": {"open": "09:00", "close": "17:00", "enabled": true}, "saturday": {"open": "09:00", "close": "17:00", "enabled": true}, "sunday": {"open": "09:00", "close": "17:00", "enabled": false}}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Clients table
CREATE TABLE clients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  whatsapp_number TEXT,
  address TEXT,
  notes TEXT,
  
  -- Client preferences and history
  preferred_communication TEXT DEFAULT 'email', -- email, whatsapp, phone
  client_since DATE DEFAULT CURRENT_DATE,
  total_bookings INTEGER DEFAULT 0,
  total_revenue DECIMAL(10,2) DEFAULT 0,
  
  -- Social media and referral tracking
  instagram_handle TEXT,
  how_heard_about_us TEXT,
  referral_source TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bookings table
CREATE TABLE bookings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_reference TEXT UNIQUE NOT NULL,
  client_id UUID REFERENCES clients(id) ON DELETE SET NULL,
  
  -- Client information (denormalized for data integrity)
  client_name TEXT NOT NULL,
  client_email TEXT,
  client_phone TEXT,
  
  -- Event details
  event_type TEXT NOT NULL, -- engagement, wedding, homecoming, wedding-homecoming, triple-combo
  event_date DATE,
  event_end_date DATE, -- for multi-day events
  venue TEXT,
  venue_address TEXT,
  
  -- Package and pricing
  package_type TEXT,
  package_price DECIMAL(10,2),
  additional_services JSONB DEFAULT '[]',
  total_amount DECIMAL(10,2),
  
  -- Booking status and workflow
  status TEXT DEFAULT 'pending', -- pending, confirmed, rejected, completed, cancelled
  priority TEXT DEFAULT 'normal', -- low, normal, high, urgent
  photographer_notes TEXT,
  client_notes TEXT,
  
  -- Complete form data (preserves all original form fields)
  form_data JSONB NOT NULL,
  
  -- Communication tracking
  email_sent BOOLEAN DEFAULT FALSE,
  whatsapp_sent BOOLEAN DEFAULT FALSE,
  last_communication_at TIMESTAMP WITH TIME ZONE,
  communication_count INTEGER DEFAULT 0,
  
  -- Scheduling details
  ceremony_time TIME,
  event_end_time TIME,
  preparation_time TIME,
  travel_time_minutes INTEGER DEFAULT 0,
  
  -- Financial tracking
  deposit_amount DECIMAL(10,2) DEFAULT 0,
  deposit_paid BOOLEAN DEFAULT FALSE,
  deposit_paid_at TIMESTAMP WITH TIME ZONE,
  final_payment_due DATE,
  final_payment_paid BOOLEAN DEFAULT FALSE,
  final_payment_paid_at TIMESTAMP WITH TIME ZONE,
  
  -- Delivery and completion
  photos_delivered BOOLEAN DEFAULT FALSE,
  photos_delivered_at TIMESTAMP WITH TIME ZONE,
  client_feedback TEXT,
  client_rating INTEGER CHECK (client_rating >= 1 AND client_rating <= 5),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Communication log table
CREATE TABLE communication_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
  
  communication_type TEXT NOT NULL, -- email, whatsapp, phone, in_person, other
  direction TEXT NOT NULL, -- outgoing, incoming
  subject TEXT,
  message TEXT,
  
  -- Metadata
  sent_via TEXT, -- emailjs, whatsapp_web, manual, etc.
  external_id TEXT, -- reference to external service
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Package templates table
CREATE TABLE package_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  base_price DECIMAL(10,2) NOT NULL,
  
  -- Package details
  duration_hours INTEGER,
  included_services JSONB DEFAULT '[]',
  deliverables JSONB DEFAULT '[]',
  
  -- Availability
  available_for_event_types JSONB DEFAULT '["engagement", "wedding", "homecoming"]',
  is_active BOOLEAN DEFAULT TRUE,
  display_order INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Settings table for various configuration options
CREATE TABLE settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  setting_key TEXT UNIQUE NOT NULL,
  setting_value JSONB,
  description TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics and metrics table
CREATE TABLE analytics_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_type TEXT NOT NULL, -- booking_created, booking_confirmed, client_added, etc.
  event_data JSONB,
  
  -- Context
  booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
  client_id UUID REFERENCES clients(id) ON DELETE SET NULL,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_bookings_event_date ON bookings(event_date);
CREATE INDEX idx_bookings_client_id ON bookings(client_id);
CREATE INDEX idx_bookings_created_at ON bookings(created_at);
CREATE INDEX idx_bookings_reference ON bookings(booking_reference);

CREATE INDEX idx_clients_email ON clients(email);
CREATE INDEX idx_clients_phone ON clients(phone);
CREATE INDEX idx_clients_created_at ON clients(created_at);

CREATE INDEX idx_communication_log_booking_id ON communication_log(booking_id);
CREATE INDEX idx_communication_log_client_id ON communication_log(client_id);
CREATE INDEX idx_communication_log_created_at ON communication_log(created_at);

CREATE INDEX idx_analytics_events_type ON analytics_events(event_type);
CREATE INDEX idx_analytics_events_created_at ON analytics_events(created_at);
