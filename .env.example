# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME="Photographer Dashboard"

# EmailJS Configuration (existing - for demo)
NEXT_PUBLIC_EMAILJS_SERVICE_ID=service_ywznx8m
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=template_rae6acc
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=pyjj4z90GoTsyHDQT

# WhatsApp Configuration (existing - for demo)
NEXT_PUBLIC_DEFAULT_WHATSAPP=94715768552

# Optional: Google Calendar API (for future integration)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Optional: Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=your_google_analytics_id
