#!/bin/bash

# Standalone Photographer Dashboard Setup Script
# This script helps set up the dashboard system for production use

set -e

echo "🎨 Photographer Dashboard Setup"
echo "================================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "⚠️  Creating .env.local from template..."
    cp .env.example .env.local
    echo "📝 Please edit .env.local with your Supabase credentials"
    echo "   Required variables:"
    echo "   - NEXT_PUBLIC_SUPABASE_URL"
    echo "   - NEXT_PUBLIC_SUPABASE_ANON_KEY"
    echo "   - SUPABASE_SERVICE_ROLE_KEY"
    echo ""
    read -p "Press Enter after updating .env.local..."
fi

# Build the application
echo "🔨 Building application..."
npm run build

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi

echo ""
echo "🎉 Dashboard setup complete!"
echo ""
echo "Next steps:"
echo "1. Set up your Supabase database using the SQL files in lib/supabase/"
echo "2. Configure your EmailJS account (optional)"
echo "3. Start the development server: npm run dev"
echo "4. Access the dashboard at: http://localhost:3000/dashboard"
echo ""
echo "For production deployment:"
echo "- Deploy to Vercel: vercel --prod"
echo "- Or deploy to Netlify: netlify deploy --prod"
echo ""
echo "📚 See README-DASHBOARD.md for detailed documentation"
