import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright Configuration for Tera Works Photographer Booking System
 * Comprehensive testing setup for booking form, dashboard, and integrations
 */
export default defineConfig({
  // Test directory
  testDir: './tests',
  
  // Run tests in files in parallel
  fullyParallel: true,
  
  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,
  
  // Retry on CI only
  retries: process.env.CI ? 2 : 0,
  
  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : undefined,
  
  // Reporter to use
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['line'],
    ...(process.env.CI ? [['github']] : [])
  ],
  
  // Shared settings for all the projects below
  use: {
    // Base URL to use in actions like `await page.goto('/')`
    baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000',
    
    // Collect trace when retrying the failed test
    trace: 'on-first-retry',
    
    // Record video on failure
    video: 'retain-on-failure',
    
    // Take screenshot on failure
    screenshot: 'only-on-failure',
    
    // Global timeout for each action
    actionTimeout: 30000,
    
    // Global timeout for navigation
    navigationTimeout: 30000,
  },

  // Configure projects for major browsers
  projects: [
    // Setup project for authentication
    {
      name: 'setup',
      testMatch: /.*\.setup\.ts/,
    },

    // Desktop Chrome
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // Use prepared auth state
        storageState: 'tests/auth/photographer.json',
      },
      dependencies: ['setup'],
    },

    // Desktop Firefox
    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        storageState: 'tests/auth/photographer.json',
      },
      dependencies: ['setup'],
    },

    // Desktop Safari
    {
      name: 'webkit',
      use: { 
        ...devices['Desktop Safari'],
        storageState: 'tests/auth/photographer.json',
      },
      dependencies: ['setup'],
    },

    // Mobile Chrome (320px - specified in requirements)
    {
      name: 'Mobile Chrome 320px',
      use: { 
        ...devices['Pixel 5'],
        viewport: { width: 320, height: 568 },
      },
      testMatch: /.*mobile.*\.spec\.ts/,
    },

    // Mobile Chrome (375px - specified in requirements)
    {
      name: 'Mobile Chrome 375px',
      use: { 
        ...devices['iPhone 12'],
        viewport: { width: 375, height: 812 },
      },
      testMatch: /.*mobile.*\.spec\.ts/,
    },

    // Mobile Chrome (414px - specified in requirements)
    {
      name: 'Mobile Chrome 414px',
      use: { 
        ...devices['iPhone 12 Pro Max'],
        viewport: { width: 414, height: 896 },
      },
      testMatch: /.*mobile.*\.spec\.ts/,
    },

    // Tablet (768px - specified in requirements)
    {
      name: 'Tablet 768px',
      use: { 
        ...devices['iPad'],
        viewport: { width: 768, height: 1024 },
      },
      testMatch: /.*tablet.*\.spec\.ts/,
    },

    // Unauthenticated tests (for booking form)
    {
      name: 'booking-form-tests',
      use: { 
        ...devices['Desktop Chrome'],
        // No auth state for public booking form
      },
      testMatch: /.*booking.*\.spec\.ts/,
    },
  ],

  // Global setup and teardown
  globalSetup: require.resolve('./tests/global-setup.ts'),
  globalTeardown: require.resolve('./tests/global-teardown.ts'),

  // Run your local dev server before starting the tests
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000, // 2 minutes
  },

  // Test timeout
  timeout: 60 * 1000, // 1 minute per test

  // Expect timeout
  expect: {
    timeout: 10 * 1000, // 10 seconds for assertions
  },

  // Output directory for test artifacts
  outputDir: 'test-results/',
});
