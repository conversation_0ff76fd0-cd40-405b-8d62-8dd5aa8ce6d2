import { test as setup, expect } from '@playwright/test';
import { createTestPhotographer } from '../utils/test-helpers';

/**
 * Authentication Setup for Tests
 * Creates and authenticates test photographer account
 */

const authFile = 'tests/auth/photographer.json';

setup('authenticate photographer', async ({ page }) => {
  console.log('🔐 Setting up photographer authentication...');

  // Create test photographer if needed
  const testPhotographer = await createTestPhotographer();
  
  await setup.step('Navigate to login page', async () => {
    await page.goto('/dashboard/login');
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
  });

  await setup.step('Check if signup is needed', async () => {
    const signUpLink = page.locator('text=Sign up');
    const isSignUpVisible = await signUpLink.isVisible();
    
    if (isSignUpVisible) {
      console.log('📝 Creating new photographer account...');
      
      await signUpLink.click();
      await page.fill('[data-testid="email"]', testPhotographer.email);
      await page.fill('[data-testid="password"]', testPhotographer.password);
      await page.fill('[data-testid="business-name"]', testPhotographer.profile.business_name);
      await page.fill('[data-testid="owner-name"]', testPhotographer.profile.owner_name);
      await page.click('[data-testid="signup-button"]');
      
      // Wait for account creation
      await page.waitForTimeout(3000);
      
      // If redirected to login, go back to login
      if (page.url().includes('/login')) {
        console.log('🔄 Redirected to login after signup');
      }
    }
  });

  await setup.step('Login with photographer credentials', async () => {
    // Ensure we're on login page
    if (!page.url().includes('/login')) {
      await page.goto('/dashboard/login');
    }
    
    await page.fill('[data-testid="email"]', testPhotographer.email);
    await page.fill('[data-testid="password"]', testPhotographer.password);
    await page.click('[data-testid="login-button"]');
  });

  await setup.step('Verify successful login', async () => {
    await page.waitForURL('**/dashboard', { timeout: 30000 });
    await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
    console.log('✅ Photographer authentication successful');
  });

  await setup.step('Save authentication state', async () => {
    await page.context().storageState({ path: authFile });
    console.log('💾 Authentication state saved to', authFile);
  });

  // Set environment variables for other tests
  process.env.TEST_PHOTOGRAPHER_EMAIL = testPhotographer.email;
  process.env.TEST_PHOTOGRAPHER_PASSWORD = testPhotographer.password;
});
