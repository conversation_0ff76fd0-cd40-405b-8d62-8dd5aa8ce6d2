export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      photographer_profile: {
        Row: {
          id: string
          email: string
          business_name: string
          owner_name: string
          phone: string | null
          whatsapp_number: string | null
          logo_url: string | null
          website_url: string | null
          address: string | null
          bio: string | null
          emailjs_service_id: string | null
          emailjs_template_id: string | null
          emailjs_public_key: string | null
          timezone: string
          currency: string
          business_hours: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          business_name: string
          owner_name: string
          phone?: string | null
          whatsapp_number?: string | null
          logo_url?: string | null
          website_url?: string | null
          address?: string | null
          bio?: string | null
          emailjs_service_id?: string | null
          emailjs_template_id?: string | null
          emailjs_public_key?: string | null
          timezone?: string
          currency?: string
          business_hours?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          business_name?: string
          owner_name?: string
          phone?: string | null
          whatsapp_number?: string | null
          logo_url?: string | null
          website_url?: string | null
          address?: string | null
          bio?: string | null
          emailjs_service_id?: string | null
          emailjs_template_id?: string | null
          emailjs_public_key?: string | null
          timezone?: string
          currency?: string
          business_hours?: Json
          created_at?: string
          updated_at?: string
        }
      }
      clients: {
        Row: {
          id: string
          name: string
          email: string | null
          phone: string | null
          whatsapp_number: string | null
          address: string | null
          notes: string | null
          preferred_communication: string
          client_since: string
          total_bookings: number
          total_revenue: number
          instagram_handle: string | null
          how_heard_about_us: string | null
          referral_source: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email?: string | null
          phone?: string | null
          whatsapp_number?: string | null
          address?: string | null
          notes?: string | null
          preferred_communication?: string
          client_since?: string
          total_bookings?: number
          total_revenue?: number
          instagram_handle?: string | null
          how_heard_about_us?: string | null
          referral_source?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string | null
          phone?: string | null
          whatsapp_number?: string | null
          address?: string | null
          notes?: string | null
          preferred_communication?: string
          client_since?: string
          total_bookings?: number
          total_revenue?: number
          instagram_handle?: string | null
          how_heard_about_us?: string | null
          referral_source?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      bookings: {
        Row: {
          id: string
          booking_reference: string
          client_id: string | null
          client_name: string
          client_email: string | null
          client_phone: string | null
          event_type: string
          event_date: string | null
          event_end_date: string | null
          venue: string | null
          venue_address: string | null
          package_type: string | null
          package_price: number | null
          additional_services: Json
          total_amount: number | null
          status: string
          priority: string
          photographer_notes: string | null
          client_notes: string | null
          form_data: Json
          email_sent: boolean
          whatsapp_sent: boolean
          last_communication_at: string | null
          communication_count: number
          ceremony_time: string | null
          event_end_time: string | null
          preparation_time: string | null
          travel_time_minutes: number
          deposit_amount: number
          deposit_paid: boolean
          deposit_paid_at: string | null
          final_payment_due: string | null
          final_payment_paid: boolean
          final_payment_paid_at: string | null
          photos_delivered: boolean
          photos_delivered_at: string | null
          client_feedback: string | null
          client_rating: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          booking_reference: string
          client_id?: string | null
          client_name: string
          client_email?: string | null
          client_phone?: string | null
          event_type: string
          event_date?: string | null
          event_end_date?: string | null
          venue?: string | null
          venue_address?: string | null
          package_type?: string | null
          package_price?: number | null
          additional_services?: Json
          total_amount?: number | null
          status?: string
          priority?: string
          photographer_notes?: string | null
          client_notes?: string | null
          form_data: Json
          email_sent?: boolean
          whatsapp_sent?: boolean
          last_communication_at?: string | null
          communication_count?: number
          ceremony_time?: string | null
          event_end_time?: string | null
          preparation_time?: string | null
          travel_time_minutes?: number
          deposit_amount?: number
          deposit_paid?: boolean
          deposit_paid_at?: string | null
          final_payment_due?: string | null
          final_payment_paid?: boolean
          final_payment_paid_at?: string | null
          photos_delivered?: boolean
          photos_delivered_at?: string | null
          client_feedback?: string | null
          client_rating?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          booking_reference?: string
          client_id?: string | null
          client_name?: string
          client_email?: string | null
          client_phone?: string | null
          event_type?: string
          event_date?: string | null
          event_end_date?: string | null
          venue?: string | null
          venue_address?: string | null
          package_type?: string | null
          package_price?: number | null
          additional_services?: Json
          total_amount?: number | null
          status?: string
          priority?: string
          photographer_notes?: string | null
          client_notes?: string | null
          form_data?: Json
          email_sent?: boolean
          whatsapp_sent?: boolean
          last_communication_at?: string | null
          communication_count?: number
          ceremony_time?: string | null
          event_end_time?: string | null
          preparation_time?: string | null
          travel_time_minutes?: number
          deposit_amount?: number
          deposit_paid?: boolean
          deposit_paid_at?: string | null
          final_payment_due?: string | null
          final_payment_paid?: boolean
          final_payment_paid_at?: string | null
          photos_delivered?: boolean
          photos_delivered_at?: string | null
          client_feedback?: string | null
          client_rating?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      communication_log: {
        Row: {
          id: string
          booking_id: string | null
          client_id: string | null
          communication_type: string
          direction: string
          subject: string | null
          message: string | null
          sent_via: string | null
          external_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          booking_id?: string | null
          client_id?: string | null
          communication_type: string
          direction: string
          subject?: string | null
          message?: string | null
          sent_via?: string | null
          external_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          booking_id?: string | null
          client_id?: string | null
          communication_type?: string
          direction?: string
          subject?: string | null
          message?: string | null
          sent_via?: string | null
          external_id?: string | null
          created_at?: string
        }
      }
      package_templates: {
        Row: {
          id: string
          name: string
          description: string | null
          base_price: number
          duration_hours: number | null
          included_services: Json
          deliverables: Json
          available_for_event_types: Json
          is_active: boolean
          display_order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          base_price: number
          duration_hours?: number | null
          included_services?: Json
          deliverables?: Json
          available_for_event_types?: Json
          is_active?: boolean
          display_order?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          base_price?: number
          duration_hours?: number | null
          included_services?: Json
          deliverables?: Json
          available_for_event_types?: Json
          is_active?: boolean
          display_order?: number
          created_at?: string
          updated_at?: string
        }
      }
      settings: {
        Row: {
          id: string
          setting_key: string
          setting_value: Json | null
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          setting_key: string
          setting_value?: Json | null
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          setting_key?: string
          setting_value?: Json | null
          description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      analytics_events: {
        Row: {
          id: string
          event_type: string
          event_data: Json | null
          booking_id: string | null
          client_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          event_type: string
          event_data?: Json | null
          booking_id?: string | null
          client_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          event_type?: string
          event_data?: Json | null
          booking_id?: string | null
          client_id?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      generate_booking_reference: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      log_analytics_event: {
        Args: {
          p_event_type: string
          p_event_data?: Json
          p_booking_id?: string
          p_client_id?: string
        }
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
