import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase/client';
import { Database } from '@/lib/supabase/types';
import { useDashboardStore } from '@/lib/store/dashboard-store';

type Booking = Database['public']['Tables']['bookings']['Row'];
type BookingInsert = Database['public']['Tables']['bookings']['Insert'];
type BookingUpdate = Database['public']['Tables']['bookings']['Update'];

// Query keys
export const bookingKeys = {
  all: ['bookings'] as const,
  lists: () => [...bookingKeys.all, 'list'] as const,
  list: (filters: any) => [...bookingKeys.lists(), filters] as const,
  details: () => [...bookingKeys.all, 'detail'] as const,
  detail: (id: string) => [...bookingKeys.details(), id] as const,
};

// Fetch all bookings
export function useBookings() {
  const { actions } = useDashboardStore();
  
  return useQuery({
    queryKey: bookingKeys.lists(),
    queryFn: async (): Promise<Booking[]> => {
      actions.setLoading('bookings', true);
      
      try {
        const { data, error } = await supabase
          .from('bookings')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) throw error;

        // Update store
        actions.setBookings(data || []);
        
        return data || [];
      } finally {
        actions.setLoading('bookings', false);
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Fetch single booking
export function useBooking(id: string) {
  return useQuery({
    queryKey: bookingKeys.detail(id),
    queryFn: async (): Promise<Booking | null> => {
      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id,
  });
}

// Create booking mutation
export function useCreateBooking() {
  const queryClient = useQueryClient();
  const { actions } = useDashboardStore();

  return useMutation({
    mutationFn: async (booking: BookingInsert): Promise<Booking> => {
      // Generate booking reference if not provided
      if (!booking.booking_reference) {
        const { data: reference } = await supabase.rpc('generate_booking_reference');
        booking.booking_reference = reference;
      }

      const { data, error } = await supabase
        .from('bookings')
        .insert(booking)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Update cache
      queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
      
      // Update store
      actions.addBooking(data);
      
      // Add notification
      actions.addNotification({
        type: 'success',
        title: 'Booking Created',
        message: `Booking ${data.booking_reference} has been created successfully.`,
      });

      // Log analytics
      supabase.rpc('log_analytics_event', {
        p_event_type: 'booking_created',
        p_event_data: { booking_id: data.id, event_type: data.event_type },
        p_booking_id: data.id,
      });
    },
    onError: (error) => {
      actions.addNotification({
        type: 'error',
        title: 'Error Creating Booking',
        message: error.message || 'Failed to create booking. Please try again.',
      });
    },
  });
}

// Update booking mutation
export function useUpdateBooking() {
  const queryClient = useQueryClient();
  const { actions } = useDashboardStore();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: BookingUpdate }): Promise<Booking> => {
      const { data, error } = await supabase
        .from('bookings')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Update cache
      queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
      queryClient.invalidateQueries({ queryKey: bookingKeys.detail(data.id) });
      
      // Update store
      actions.updateBooking(data.id, data);
      
      // Add notification
      actions.addNotification({
        type: 'success',
        title: 'Booking Updated',
        message: `Booking ${data.booking_reference} has been updated successfully.`,
      });

      // Log analytics
      supabase.rpc('log_analytics_event', {
        p_event_type: 'booking_updated',
        p_event_data: { booking_id: data.id, status: data.status },
        p_booking_id: data.id,
      });
    },
    onError: (error) => {
      actions.addNotification({
        type: 'error',
        title: 'Error Updating Booking',
        message: error.message || 'Failed to update booking. Please try again.',
      });
    },
  });
}

// Delete booking mutation
export function useDeleteBooking() {
  const queryClient = useQueryClient();
  const { actions } = useDashboardStore();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: (_, id) => {
      // Update cache
      queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
      
      // Update store
      const { bookings } = useDashboardStore.getState();
      const updatedBookings = bookings.filter(booking => booking.id !== id);
      actions.setBookings(updatedBookings);
      
      // Clear selection if deleted booking was selected
      const { selectedBooking } = useDashboardStore.getState();
      if (selectedBooking?.id === id) {
        actions.selectBooking(null);
      }
      
      // Add notification
      actions.addNotification({
        type: 'success',
        title: 'Booking Deleted',
        message: 'Booking has been deleted successfully.',
      });
    },
    onError: (error) => {
      actions.addNotification({
        type: 'error',
        title: 'Error Deleting Booking',
        message: error.message || 'Failed to delete booking. Please try again.',
      });
    },
  });
}

// Real-time subscription hook
export function useBookingSubscription() {
  const queryClient = useQueryClient();
  const { actions } = useDashboardStore();

  return useQuery({
    queryKey: ['booking-subscription'],
    queryFn: () => {
      const channel = supabase
        .channel('bookings-changes')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'bookings',
          },
          (payload) => {
            console.log('New booking received:', payload.new);
            
            // Add to store
            actions.addBooking(payload.new as Booking);
            
            // Invalidate queries
            queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
            
            // Show notification
            actions.addNotification({
              type: 'info',
              title: 'New Booking Received',
              message: `New booking from ${(payload.new as Booking).client_name}`,
            });
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'bookings',
          },
          (payload) => {
            console.log('Booking updated:', payload.new);
            
            // Update store
            actions.updateBooking(payload.new.id, payload.new as Booking);
            
            // Invalidate queries
            queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
            queryClient.invalidateQueries({ queryKey: bookingKeys.detail(payload.new.id) });
          }
        )
        .subscribe();

      return channel;
    },
    staleTime: Infinity,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
}
