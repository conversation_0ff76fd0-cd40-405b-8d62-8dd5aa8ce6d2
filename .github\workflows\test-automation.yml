name: 🧪 Automated Testing - Tera Works Photographer Booking System

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_suite:
        description: 'Test suite to run'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - booking
          - dashboard
          - integration
          - audit
          - mobile

env:
  NODE_VERSION: '18'
  PLAYWRIGHT_BROWSERS_PATH: ${{ github.workspace }}/ms-playwright

jobs:
  # Setup and preparation
  setup:
    name: 🚀 Setup Test Environment
    runs-on: ubuntu-latest
    outputs:
      cache-key: ${{ steps.cache-key.outputs.key }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔧 Generate cache key
        id: cache-key
        run: echo "key=playwright-${{ runner.os }}-${{ hashFiles('package-lock.json') }}" >> $GITHUB_OUTPUT

      - name: 📚 Cache Playwright browsers
        uses: actions/cache@v3
        with:
          path: ${{ env.PLAYWRIGHT_BROWSERS_PATH }}
          key: ${{ steps.cache-key.outputs.key }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🎭 Install Playwright browsers
        run: npx playwright install --with-deps

  # Booking form tests
  test-booking:
    name: 📋 Booking Form Tests
    runs-on: ubuntu-latest
    needs: setup
    if: ${{ github.event.inputs.test_suite == 'all' || github.event.inputs.test_suite == 'booking' || github.event.inputs.test_suite == '' }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📚 Restore Playwright browsers
        uses: actions/cache@v3
        with:
          path: ${{ env.PLAYWRIGHT_BROWSERS_PATH }}
          key: ${{ needs.setup.outputs.cache-key }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Start application
        run: |
          npm run build
          npm start &
          npx wait-on http://localhost:3000 --timeout 60000

      - name: 🧪 Run booking tests
        run: npm run test:booking
        env:
          CI: true
          PLAYWRIGHT_BASE_URL: http://localhost:3000

      - name: 📊 Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: booking-test-results
          path: |
            test-results/
            playwright-report/
          retention-days: 7

  # Dashboard tests
  test-dashboard:
    name: 🎛️ Dashboard Tests
    runs-on: ubuntu-latest
    needs: setup
    if: ${{ github.event.inputs.test_suite == 'all' || github.event.inputs.test_suite == 'dashboard' || github.event.inputs.test_suite == '' }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📚 Restore Playwright browsers
        uses: actions/cache@v3
        with:
          path: ${{ env.PLAYWRIGHT_BROWSERS_PATH }}
          key: ${{ needs.setup.outputs.cache-key }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Start application
        run: |
          npm run build
          npm start &
          npx wait-on http://localhost:3000 --timeout 60000

      - name: 🧪 Run dashboard tests
        run: npm run test:dashboard
        env:
          CI: true
          PLAYWRIGHT_BASE_URL: http://localhost:3000

      - name: 📊 Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: dashboard-test-results
          path: |
            test-results/
            playwright-report/
          retention-days: 7

  # Integration tests
  test-integration:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    needs: setup
    if: ${{ github.event.inputs.test_suite == 'all' || github.event.inputs.test_suite == 'integration' || github.event.inputs.test_suite == '' }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📚 Restore Playwright browsers
        uses: actions/cache@v3
        with:
          path: ${{ env.PLAYWRIGHT_BROWSERS_PATH }}
          key: ${{ needs.setup.outputs.cache-key }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Start application
        run: |
          npm run build
          npm start &
          npx wait-on http://localhost:3000 --timeout 60000

      - name: 🧪 Run integration tests
        run: npm run test:integration
        env:
          CI: true
          PLAYWRIGHT_BASE_URL: http://localhost:3000

      - name: 📊 Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: |
            test-results/
            playwright-report/
          retention-days: 7

  # Audit tests (Performance, Accessibility, SEO, Security)
  test-audit:
    name: 🔍 Platform Audit Tests
    runs-on: ubuntu-latest
    needs: setup
    if: ${{ github.event.inputs.test_suite == 'all' || github.event.inputs.test_suite == 'audit' || github.event.inputs.test_suite == '' }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📚 Restore Playwright browsers
        uses: actions/cache@v3
        with:
          path: ${{ env.PLAYWRIGHT_BROWSERS_PATH }}
          key: ${{ needs.setup.outputs.cache-key }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Start application
        run: |
          npm run build
          npm start &
          npx wait-on http://localhost:3000 --timeout 60000

      - name: 🧪 Run audit tests
        run: npm run test:audit
        env:
          CI: true
          PLAYWRIGHT_BASE_URL: http://localhost:3000

      - name: 📊 Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: audit-test-results
          path: |
            test-results/
            playwright-report/
          retention-days: 7

  # Mobile responsiveness tests
  test-mobile:
    name: 📱 Mobile Responsiveness Tests
    runs-on: ubuntu-latest
    needs: setup
    if: ${{ github.event.inputs.test_suite == 'all' || github.event.inputs.test_suite == 'mobile' || github.event.inputs.test_suite == '' }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📚 Restore Playwright browsers
        uses: actions/cache@v3
        with:
          path: ${{ env.PLAYWRIGHT_BROWSERS_PATH }}
          key: ${{ needs.setup.outputs.cache-key }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Start application
        run: |
          npm run build
          npm start &
          npx wait-on http://localhost:3000 --timeout 60000

      - name: 🧪 Run mobile tests
        run: npm run test:mobile
        env:
          CI: true
          PLAYWRIGHT_BASE_URL: http://localhost:3000

      - name: 📊 Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: mobile-test-results
          path: |
            test-results/
            playwright-report/
          retention-days: 7

  # Generate comprehensive report
  generate-report:
    name: 📊 Generate Test Report
    runs-on: ubuntu-latest
    needs: [test-booking, test-dashboard, test-integration, test-audit, test-mobile]
    if: always()
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 📥 Download all test results
        uses: actions/download-artifact@v3
        with:
          path: combined-results/

      - name: 📊 Generate comprehensive report
        run: |
          npm ci
          node scripts/generate-combined-report.js
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: 📤 Upload comprehensive report
        uses: actions/upload-artifact@v3
        with:
          name: comprehensive-test-report
          path: |
            combined-results/
            comprehensive-report/
          retention-days: 30

      - name: 📝 Comment on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const path = 'comprehensive-report/summary.md';
            if (fs.existsSync(path)) {
              const summary = fs.readFileSync(path, 'utf8');
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `## 🧪 Test Results Summary\n\n${summary}`
              });
            }

  # Notify on failure
  notify-failure:
    name: 📢 Notify on Failure
    runs-on: ubuntu-latest
    needs: [test-booking, test-dashboard, test-integration, test-audit, test-mobile]
    if: failure()
    steps:
      - name: 📧 Send failure notification
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#test-results'
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
