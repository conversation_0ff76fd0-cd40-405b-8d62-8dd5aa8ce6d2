import { test, expect } from '@playwright/test';

/**
 * SEO and Security Audit Tests
 * Tests search engine optimization and security vulnerabilities
 */

test.describe('SEO Optimization', () => {
  test('should have proper meta tags @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Check essential meta tags', async () => {
      // Title tag
      const title = await page.title();
      expect(title).toBeTruthy();
      expect(title.length).toBeGreaterThan(10);
      expect(title.length).toBeLessThan(60); // SEO best practice

      // Meta description
      const metaDescription = page.locator('meta[name="description"]');
      await expect(metaDescription).toHaveCount(1);
      const description = await metaDescription.getAttribute('content');
      expect(description?.length).toBeGreaterThan(50);
      expect(description?.length).toBeLessThan(160); // SEO best practice

      // Viewport meta tag
      const viewport = page.locator('meta[name="viewport"]');
      await expect(viewport).toHaveCount(1);
      const viewportContent = await viewport.getAttribute('content');
      expect(viewportContent).toContain('width=device-width');
    });

    await test.step('Check Open Graph tags', async () => {
      // OG title
      const ogTitle = page.locator('meta[property="og:title"]');
      await expect(ogTitle).toHaveCount(1);

      // OG description
      const ogDescription = page.locator('meta[property="og:description"]');
      await expect(ogDescription).toHaveCount(1);

      // OG image
      const ogImage = page.locator('meta[property="og:image"]');
      await expect(ogImage).toHaveCount(1);
      const imageUrl = await ogImage.getAttribute('content');
      expect(imageUrl).toMatch(/^https?:\/\/.+\.(jpg|jpeg|png|webp)$/i);

      // OG type
      const ogType = page.locator('meta[property="og:type"]');
      await expect(ogType).toHaveCount(1);
    });

    await test.step('Check Twitter Card tags', async () => {
      // Twitter card type
      const twitterCard = page.locator('meta[name="twitter:card"]');
      await expect(twitterCard).toHaveCount(1);

      // Twitter title
      const twitterTitle = page.locator('meta[name="twitter:title"]');
      await expect(twitterTitle).toHaveCount(1);

      // Twitter description
      const twitterDescription = page.locator('meta[name="twitter:description"]');
      await expect(twitterDescription).toHaveCount(1);
    });
  });

  test('should have proper heading structure for SEO @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Check H1 tag', async () => {
      const h1Tags = page.locator('h1');
      await expect(h1Tags).toHaveCount(1); // Should have exactly one H1
      
      const h1Text = await h1Tags.textContent();
      expect(h1Text?.length).toBeGreaterThan(10);
      expect(h1Text).toContain('Photographer'); // Should be relevant to business
    });

    await test.step('Check heading hierarchy', async () => {
      const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
      const headingTexts = await Promise.all(headings.map(h => h.textContent()));
      
      // All headings should have meaningful text
      headingTexts.forEach(text => {
        expect(text?.trim().length).toBeGreaterThan(0);
      });
    });
  });

  test('should have SEO-friendly URLs @desktop', async ({ page }) => {
    await test.step('Check main page URL', async () => {
      await page.goto('/');
      const url = page.url();
      expect(url).toMatch(/^https?:\/\/[^\/]+\/?$/); // Clean root URL
    });

    await test.step('Check dashboard URLs', async () => {
      // Login first
      await page.goto('/dashboard/login');
      await page.fill('[data-testid="email"]', process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>');
      await page.fill('[data-testid="password"]', process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!');
      await page.click('[data-testid="login-button"]');
      
      // Check dashboard URLs are clean
      await page.goto('/dashboard/bookings');
      expect(page.url()).toContain('/dashboard/bookings');
      
      await page.goto('/dashboard/clients');
      expect(page.url()).toContain('/dashboard/clients');
    });
  });

  test('should have proper canonical URLs @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Check canonical link', async () => {
      const canonical = page.locator('link[rel="canonical"]');
      await expect(canonical).toHaveCount(1);
      
      const canonicalUrl = await canonical.getAttribute('href');
      expect(canonicalUrl).toMatch(/^https?:\/\/.+/);
    });
  });

  test('should have robots.txt and sitemap @desktop', async ({ page }) => {
    await test.step('Check robots.txt', async () => {
      const robotsResponse = await page.goto('/robots.txt');
      expect(robotsResponse?.status()).toBe(200);
      
      const robotsContent = await page.textContent('body');
      expect(robotsContent).toContain('User-agent');
      expect(robotsContent).toContain('Sitemap');
    });

    await test.step('Check sitemap.xml', async () => {
      const sitemapResponse = await page.goto('/sitemap.xml');
      expect(sitemapResponse?.status()).toBe(200);
      
      const sitemapContent = await page.textContent('body');
      expect(sitemapContent).toContain('<urlset');
      expect(sitemapContent).toContain('<url>');
    });
  });

  test('should have structured data @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Check JSON-LD structured data', async () => {
      const structuredData = page.locator('script[type="application/ld+json"]');
      const count = await structuredData.count();
      
      if (count > 0) {
        const jsonContent = await structuredData.first().textContent();
        const parsedData = JSON.parse(jsonContent || '{}');
        
        // Should have proper schema.org structure
        expect(parsedData['@context']).toBe('https://schema.org');
        expect(parsedData['@type']).toBeTruthy();
      }
    });
  });

  test('should optimize images for SEO @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Check image alt attributes', async () => {
      const images = page.locator('img');
      const imageCount = await images.count();

      for (let i = 0; i < imageCount; i++) {
        const img = images.nth(i);
        const alt = await img.getAttribute('alt');
        const src = await img.getAttribute('src');
        
        // All images should have alt text (can be empty for decorative images)
        expect(alt).not.toBeNull();
        
        // Important images should have descriptive alt text
        if (src && !src.includes('icon') && !src.includes('logo')) {
          expect(alt?.length).toBeGreaterThan(0);
        }
      }
    });
  });
});

test.describe('Security Audit', () => {
  test('should have proper security headers @desktop', async ({ page }) => {
    const response = await page.goto('/');

    await test.step('Check security headers', async () => {
      const headers = response?.headers() || {};
      
      // Content Security Policy
      expect(headers['content-security-policy'] || headers['x-content-security-policy']).toBeTruthy();
      
      // X-Frame-Options
      expect(headers['x-frame-options']).toBeTruthy();
      
      // X-Content-Type-Options
      expect(headers['x-content-type-options']).toBe('nosniff');
      
      // Referrer Policy
      expect(headers['referrer-policy']).toBeTruthy();
      
      // Strict Transport Security (if HTTPS)
      if (page.url().startsWith('https://')) {
        expect(headers['strict-transport-security']).toBeTruthy();
      }
    });
  });

  test('should prevent XSS attacks @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Test XSS prevention in form inputs', async () => {
      // Navigate to contact form
      await page.click('[data-testid="event-type-engagement"]');
      await page.click('[data-testid="next-step"]');
      await page.click('[data-testid="package-basic-engagement"]');
      await page.click('[data-testid="next-step"]');
      await page.fill('[data-testid="event-date"]', '2025-12-25');
      await page.click('[data-testid="next-step"]');

      // Try to inject script in form fields
      const xssPayload = '<script>alert("XSS")</script>';
      await page.fill('[data-testid="bride-name"]', xssPayload);
      await page.fill('[data-testid="additional-notes"]', xssPayload);

      // Check that script is not executed
      const alerts = [];
      page.on('dialog', dialog => {
        alerts.push(dialog.message());
        dialog.dismiss();
      });

      await page.click('[data-testid="submit-booking"]');
      await page.waitForTimeout(2000);

      expect(alerts).toHaveLength(0); // No alerts should be triggered
    });

    await test.step('Test XSS prevention in URL parameters', async () => {
      const xssUrl = '/?name=<script>alert("XSS")</script>';
      await page.goto(xssUrl);

      // Check that script is not executed
      const pageContent = await page.content();
      expect(pageContent).not.toContain('<script>alert("XSS")</script>');
    });
  });

  test('should have CSRF protection @desktop', async ({ page }) => {
    await test.step('Check CSRF tokens in forms', async () => {
      await page.goto('/');
      
      // Navigate to contact form
      await page.click('[data-testid="event-type-engagement"]');
      await page.click('[data-testid="next-step"]');
      await page.click('[data-testid="package-basic-engagement"]');
      await page.click('[data-testid="next-step"]');
      await page.fill('[data-testid="event-date"]', '2025-12-25');
      await page.click('[data-testid="next-step"]');

      // Check for CSRF token or similar protection
      const csrfToken = page.locator('input[name="_token"], input[name="csrf_token"], meta[name="csrf-token"]');
      const hasCSRFProtection = await csrfToken.count() > 0;
      
      // Or check if using modern SameSite cookies
      const cookies = await page.context().cookies();
      const hasSecureCookies = cookies.some(cookie => 
        cookie.sameSite === 'Strict' || cookie.sameSite === 'Lax'
      );

      expect(hasCSRFProtection || hasSecureCookies).toBeTruthy();
    });
  });

  test('should validate input sanitization @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Test SQL injection prevention', async () => {
      // Navigate to contact form
      await page.click('[data-testid="event-type-engagement"]');
      await page.click('[data-testid="next-step"]');
      await page.click('[data-testid="package-basic-engagement"]');
      await page.click('[data-testid="next-step"]');
      await page.fill('[data-testid="event-date"]', '2025-12-25');
      await page.click('[data-testid="next-step"]');

      // Try SQL injection payloads
      const sqlPayloads = [
        "'; DROP TABLE bookings; --",
        "' OR '1'='1",
        "admin'--",
        "' UNION SELECT * FROM users --"
      ];

      for (const payload of sqlPayloads) {
        await page.fill('[data-testid="bride-name"]', payload);
        await page.fill('[data-testid="email"]', '<EMAIL>');
        await page.fill('[data-testid="phone"]', '+94771234567');
        await page.check('[data-testid="agree-terms"]');
        
        // Submit form and check for errors
        await page.click('[data-testid="submit-booking"]');
        
        // Should either show validation error or success (but not database error)
        const errorMessage = page.locator('[data-testid="error-message"]');
        const successMessage = page.locator('[data-testid="success-message"]');
        
        const hasError = await errorMessage.isVisible();
        const hasSuccess = await successMessage.isVisible();
        
        // Should not show database-related errors
        if (hasError) {
          const errorText = await errorMessage.textContent();
          expect(errorText?.toLowerCase()).not.toContain('sql');
          expect(errorText?.toLowerCase()).not.toContain('database');
          expect(errorText?.toLowerCase()).not.toContain('syntax');
        }
        
        // Clear form for next test
        await page.fill('[data-testid="bride-name"]', '');
      }
    });
  });

  test('should have secure authentication @desktop', async ({ page }) => {
    await page.goto('/dashboard/login');

    await test.step('Check password security requirements', async () => {
      // Test weak password
      await page.fill('[data-testid="email"]', '<EMAIL>');
      await page.fill('[data-testid="password"]', '123');
      await page.click('[data-testid="login-button"]');

      // Should show password strength error or reject weak password
      const errorMessage = page.locator('[data-testid="error-message"], [data-testid="password-error"]');
      const hasError = await errorMessage.isVisible();
      
      if (hasError) {
        const errorText = await errorMessage.textContent();
        expect(errorText?.toLowerCase()).toMatch(/password|weak|strength|characters/);
      }
    });

    await test.step('Check login rate limiting', async () => {
      // Attempt multiple failed logins
      for (let i = 0; i < 5; i++) {
        await page.fill('[data-testid="email"]', '<EMAIL>');
        await page.fill('[data-testid="password"]', 'wrongpassword');
        await page.click('[data-testid="login-button"]');
        await page.waitForTimeout(1000);
      }

      // Should show rate limiting message
      const rateLimitMessage = page.locator('[data-testid="rate-limit-error"], [data-testid="error-message"]');
      const hasRateLimit = await rateLimitMessage.isVisible();
      
      if (hasRateLimit) {
        const errorText = await rateLimitMessage.textContent();
        expect(errorText?.toLowerCase()).toMatch(/too many|rate limit|try again|blocked/);
      }
    });
  });

  test('should protect sensitive data @desktop', async ({ page }) => {
    await test.step('Check for exposed sensitive information', async () => {
      await page.goto('/');
      
      const pageContent = await page.content();
      
      // Should not expose sensitive information in HTML
      expect(pageContent.toLowerCase()).not.toContain('password');
      expect(pageContent.toLowerCase()).not.toContain('secret');
      expect(pageContent.toLowerCase()).not.toContain('private_key');
      expect(pageContent.toLowerCase()).not.toContain('api_key');
      expect(pageContent.toLowerCase()).not.toContain('database_url');
    });

    await test.step('Check console for sensitive data leaks', async () => {
      const consoleLogs: string[] = [];
      page.on('console', msg => {
        consoleLogs.push(msg.text().toLowerCase());
      });

      await page.goto('/');
      await page.waitForTimeout(2000);

      // Check console logs for sensitive information
      consoleLogs.forEach(log => {
        expect(log).not.toContain('password');
        expect(log).not.toContain('secret');
        expect(log).not.toContain('private_key');
        expect(log).not.toContain('api_key');
      });
    });
  });

  test('should have secure cookie configuration @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Check cookie security attributes', async () => {
      const cookies = await page.context().cookies();
      
      cookies.forEach(cookie => {
        // Session cookies should be secure
        if (cookie.name.toLowerCase().includes('session') || 
            cookie.name.toLowerCase().includes('auth')) {
          expect(cookie.secure).toBeTruthy();
          expect(cookie.httpOnly).toBeTruthy();
          expect(['Strict', 'Lax']).toContain(cookie.sameSite);
        }
      });
    });
  });
});
