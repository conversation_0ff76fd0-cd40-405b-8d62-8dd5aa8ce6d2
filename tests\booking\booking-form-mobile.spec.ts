import { test, expect } from '@playwright/test';
import { BookingFormPage, generateBookingData, checkMobileResponsiveness } from '../utils/test-helpers';

/**
 * Mobile Booking Form Tests
 * Tests booking form functionality across different mobile breakpoints
 */

test.describe('Booking Form - Mobile Responsiveness', () => {
  const mobileBreakpoints = [320, 375, 414]; // As specified in requirements
  
  for (const width of mobileBreakpoints) {
    test.describe(`Mobile ${width}px`, () => {
      test.beforeEach(async ({ page }) => {
        await page.setViewportSize({ width, height: 800 });
      });

      test(`should display booking form correctly on ${width}px @mobile`, async ({ page }) => {
        const bookingPage = new BookingFormPage(page);
        await bookingPage.goto();

        await test.step('Check form layout on mobile', async () => {
          // Check if form is visible and properly sized
          const form = page.locator('[data-testid="booking-form"]');
          await expect(form).toBeVisible();
          
          const formBox = await form.boundingBox();
          expect(formBox?.width).toBeLessThanOrEqual(width);
        });

        await test.step('Check mobile navigation', async () => {
          // Check if mobile menu is visible for narrow screens
          if (width <= 768) {
            await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
          }
        });

        await test.step('Check form step indicators', async () => {
          // Step indicators should be visible and properly sized
          const stepIndicators = page.locator('[data-testid="step-indicators"]');
          await expect(stepIndicators).toBeVisible();
          
          const indicatorBox = await stepIndicators.boundingBox();
          expect(indicatorBox?.width).toBeLessThanOrEqual(width - 40); // Account for padding
        });
      });

      test(`should complete engagement booking on ${width}px @mobile`, async ({ page }) => {
        const bookingData = generateBookingData('engagement');
        const bookingPage = new BookingFormPage(page);
        await bookingPage.goto();

        await test.step('Complete mobile booking flow', async () => {
          // Step 1: Event type selection
          await expect(page.locator('[data-testid="event-type-engagement"]')).toBeVisible();
          await page.click('[data-testid="event-type-engagement"]');
          await page.click('[data-testid="next-step"]');

          // Step 2: Package selection
          await expect(page.locator('[data-testid="package-selection"]')).toBeVisible();
          await page.click('[data-testid="package-basic-engagement"]');
          await page.click('[data-testid="next-step"]');

          // Step 3: Event details
          await page.fill('[data-testid="event-date"]', bookingData.eventDate);
          await page.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
          await page.selectOption('[data-testid="outfit-changes"]', bookingData.outfitChanges);
          await page.click('[data-testid="next-step"]');

          // Step 4: Contact information
          await page.fill('[data-testid="bride-name"]', bookingData.brideAName);
          await page.fill('[data-testid="groom-name"]', bookingData.groomName);
          await page.fill('[data-testid="email"]', bookingData.email);
          await page.fill('[data-testid="phone"]', bookingData.phoneNumber);
          await page.fill('[data-testid="additional-notes"]', bookingData.additionalNotes);
          await page.selectOption('[data-testid="hear-about"]', bookingData.hearAbout);
          await page.check('[data-testid="agree-terms"]');

          // Submit form
          await page.click('[data-testid="submit-booking"]');
        });

        await test.step('Verify mobile success page', async () => {
          await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
          await expect(page.locator('[data-testid="booking-reference"]')).toBeVisible();
          
          // Check if WhatsApp button is properly sized for mobile
          const whatsappButton = page.locator('[data-testid="whatsapp-button"]');
          await expect(whatsappButton).toBeVisible();
          
          const buttonBox = await whatsappButton.boundingBox();
          expect(buttonBox?.width).toBeLessThanOrEqual(width - 40);
        });
      });

      test(`should handle form validation on ${width}px @mobile`, async ({ page }) => {
        const bookingPage = new BookingFormPage(page);
        await bookingPage.goto();

        await test.step('Navigate to contact form', async () => {
          await page.click('[data-testid="event-type-engagement"]');
          await page.click('[data-testid="next-step"]');
          await page.click('[data-testid="package-basic-engagement"]');
          await page.click('[data-testid="next-step"]');
          await page.fill('[data-testid="event-date"]', '2025-12-25');
          await page.click('[data-testid="next-step"]');
        });

        await test.step('Submit empty form and check errors', async () => {
          await page.click('[data-testid="submit-booking"]');
          
          // Check if error messages are visible and properly positioned
          const errorMessages = page.locator('[data-testid*="error"]');
          await expect(errorMessages.first()).toBeVisible();
          
          // Ensure error messages don't overflow on mobile
          const errors = await errorMessages.all();
          for (const error of errors) {
            const errorBox = await error.boundingBox();
            if (errorBox) {
              expect(errorBox.width).toBeLessThanOrEqual(width - 40);
            }
          }
        });
      });

      test(`should handle touch interactions on ${width}px @mobile`, async ({ page }) => {
        const bookingPage = new BookingFormPage(page);
        await bookingPage.goto();

        await test.step('Test touch interactions', async () => {
          // Test tap on event type cards
          const engagementCard = page.locator('[data-testid="event-type-engagement"]');
          await engagementCard.tap();
          await expect(engagementCard).toHaveClass(/selected/);

          // Test swipe gestures on package carousel (if implemented)
          const packageContainer = page.locator('[data-testid="package-container"]');
          if (await packageContainer.isVisible()) {
            const containerBox = await packageContainer.boundingBox();
            if (containerBox) {
              // Simulate swipe gesture
              await page.mouse.move(containerBox.x + containerBox.width - 50, containerBox.y + containerBox.height / 2);
              await page.mouse.down();
              await page.mouse.move(containerBox.x + 50, containerBox.y + containerBox.height / 2);
              await page.mouse.up();
            }
          }
        });
      });
    });
  }
});

test.describe('Booking Form - Tablet Responsiveness', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 }); // Tablet breakpoint
  });

  test('should display booking form correctly on tablet @tablet', async ({ page }) => {
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Check tablet layout', async () => {
      // Form should use tablet-optimized layout
      const form = page.locator('[data-testid="booking-form"]');
      await expect(form).toBeVisible();
      
      const formBox = await form.boundingBox();
      expect(formBox?.width).toBeLessThanOrEqual(768);
      expect(formBox?.width).toBeGreaterThan(600); // Should use more space than mobile
    });

    await test.step('Check tablet navigation', async () => {
      // Should show desktop-style navigation on tablet
      await expect(page.locator('[data-testid="desktop-nav"]')).toBeVisible();
    });

    await test.step('Check form layout optimization', async () => {
      // Package cards should be in 2-column layout on tablet
      const packageCards = page.locator('[data-testid^="package-"]');
      const firstCard = packageCards.first();
      const secondCard = packageCards.nth(1);
      
      if (await firstCard.isVisible() && await secondCard.isVisible()) {
        const firstBox = await firstCard.boundingBox();
        const secondBox = await secondCard.boundingBox();
        
        // Cards should be side by side on tablet
        if (firstBox && secondBox) {
          expect(Math.abs(firstBox.y - secondBox.y)).toBeLessThan(50); // Same row
        }
      }
    });
  });

  test('should complete wedding booking on tablet @tablet', async ({ page }) => {
    const bookingData = generateBookingData('wedding');
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Complete tablet wedding booking', async () => {
      // Step 1: Event type
      await page.click('[data-testid="event-type-wedding"]');
      await page.click('[data-testid="next-step"]');

      // Step 2: Package
      await page.click('[data-testid="package-premium-wedding"]');
      await page.click('[data-testid="next-step"]');

      // Step 3: Wedding details (should have better layout on tablet)
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="registration-time"]', bookingData.registrationTime);
      await page.fill('[data-testid="ceremony-time"]', bookingData.ceremonyTime);
      await page.fill('[data-testid="event-end-time"]', bookingData.eventEndTime);
      await page.fill('[data-testid="guest-count"]', bookingData.guestCount);
      await page.click('[data-testid="next-step"]');

      // Step 4: Contact information
      await page.fill('[data-testid="bride-name"]', bookingData.brideAName);
      await page.fill('[data-testid="groom-name"]', bookingData.groomName);
      await page.fill('[data-testid="email"]', bookingData.email);
      await page.fill('[data-testid="phone"]', bookingData.phoneNumber);
      await page.check('[data-testid="agree-terms"]');

      await page.click('[data-testid="submit-booking"]');
    });

    await test.step('Verify tablet success experience', async () => {
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
      
      // Success page should have optimized layout for tablet
      const successContainer = page.locator('[data-testid="success-container"]');
      const containerBox = await successContainer.boundingBox();
      expect(containerBox?.width).toBeLessThanOrEqual(768);
      expect(containerBox?.width).toBeGreaterThan(600);
    });
  });
});

test.describe('Booking Form - Cross-Device Consistency', () => {
  const testBreakpoints = [320, 375, 414, 768, 1024, 1440];

  test('should maintain consistent functionality across all breakpoints', async ({ page }) => {
    const bookingData = generateBookingData('engagement');
    
    for (const width of testBreakpoints) {
      await test.step(`Test functionality at ${width}px`, async () => {
        await page.setViewportSize({ width, height: 800 });
        
        const bookingPage = new BookingFormPage(page);
        await bookingPage.goto();

        // Basic functionality should work at all breakpoints
        await expect(page.locator('[data-testid="event-type-engagement"]')).toBeVisible();
        await page.click('[data-testid="event-type-engagement"]');
        await expect(page.locator('[data-testid="next-step"]')).toBeVisible();
        
        // Take screenshot for visual comparison
        await page.screenshot({ 
          path: `test-results/screenshots/booking-form-${width}px.png`,
          fullPage: true 
        });
      });
    }
  });

  test('should handle orientation changes on mobile devices', async ({ page }) => {
    // Test portrait to landscape transition
    await page.setViewportSize({ width: 375, height: 812 }); // iPhone 12 portrait
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Test portrait mode', async () => {
      await expect(page.locator('[data-testid="booking-form"]')).toBeVisible();
      await page.screenshot({ path: 'test-results/screenshots/portrait-mode.png' });
    });

    await test.step('Test landscape mode', async () => {
      await page.setViewportSize({ width: 812, height: 375 }); // iPhone 12 landscape
      await page.waitForTimeout(1000); // Allow layout to adjust
      
      await expect(page.locator('[data-testid="booking-form"]')).toBeVisible();
      await page.screenshot({ path: 'test-results/screenshots/landscape-mode.png' });
      
      // Form should still be functional in landscape
      await expect(page.locator('[data-testid="event-type-engagement"]')).toBeVisible();
    });
  });
});
