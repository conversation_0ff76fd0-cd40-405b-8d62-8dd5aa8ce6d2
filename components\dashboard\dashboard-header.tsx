'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  Menu, 
  Search, 
  Bell, 
  Wifi, 
  WifiOff,
  Sun,
  Moon,
  Command
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { useDashboardStore } from '@/lib/store/dashboard-store';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface DashboardHeaderProps {
  onToggleSidebar: () => void;
  onOpenCommandPalette: () => void;
  realtimeStatus: boolean;
}

const pageTitle = {
  'dashboard': 'Dashboard Overview',
  'bookings': 'Booking Management',
  'clients': 'Client Management', 
  'analytics': 'Business Analytics',
  'settings': 'Settings & Preferences'
};

export function DashboardHeader({ 
  onToggleSidebar, 
  onOpenCommandPalette,
  realtimeStatus 
}: DashboardHeaderProps) {
  const pathname = usePathname();
  const { theme, setTheme } = useTheme();
  const { ui, actions } = useDashboardStore();
  const [mounted, setMounted] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    setMounted(true);
  }, []);

  const currentPage = pathname.split('/').pop() || 'dashboard';
  const title = pageTitle[currentPage as keyof typeof pageTitle] || 'Dashboard';
  const unreadNotifications = ui.notifications.filter(n => !n.read).length;

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Implement search functionality
      console.log('Searching for:', searchQuery);
    }
  };

  const handleNotificationClick = () => {
    // Mark all notifications as read
    ui.notifications.forEach(notification => {
      if (!notification.read) {
        actions.markNotificationRead(notification.id);
      }
    });
  };

  return (
    <header className="h-16 border-b border-white/10 bg-white/5 backdrop-blur-xl">
      <div className="h-full px-6 flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center gap-4">
          {/* Sidebar Toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleSidebar}
            className="hover:bg-white/10"
          >
            <Menu className="w-5 h-5" />
          </Button>

          {/* Page Title */}
          <div>
            <motion.h1 
              key={currentPage}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="text-xl font-bold text-gray-900"
            >
              {title}
            </motion.h1>
            <p className="text-sm text-gray-600">
              {new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
        </div>

        {/* Center Section - Search */}
        <div className="flex-1 max-w-md mx-8">
          <form onSubmit={handleSearch} className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search bookings, clients... (⌘K)"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 glass border-white/20 bg-white/10 placeholder:text-gray-500"
              onClick={onOpenCommandPalette}
              readOnly
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <kbd className="px-2 py-1 text-xs bg-white/20 rounded border border-white/30 text-gray-600">
                <Command className="w-3 h-3 inline mr-1" />
                K
              </kbd>
            </div>
          </form>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-2">
          {/* Real-time Status */}
          <div className="flex items-center gap-2 px-3 py-1.5 rounded-lg glass border border-white/20">
            {realtimeStatus ? (
              <>
                <Wifi className="w-4 h-4 text-green-600" />
                <span className="text-xs font-medium text-green-700">Live</span>
              </>
            ) : (
              <>
                <WifiOff className="w-4 h-4 text-amber-600" />
                <span className="text-xs font-medium text-amber-700">Offline</span>
              </>
            )}
          </div>

          {/* Theme Toggle */}
          {mounted && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
              className="hover:bg-white/10"
            >
              {theme === 'dark' ? (
                <Sun className="w-5 h-5" />
              ) : (
                <Moon className="w-5 h-5" />
              )}
            </Button>
          )}

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="relative hover:bg-white/10"
                onClick={handleNotificationClick}
              >
                <Bell className="w-5 h-5" />
                {unreadNotifications > 0 && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-1 -right-1"
                  >
                    <Badge 
                      variant="destructive" 
                      className="w-5 h-5 p-0 flex items-center justify-center text-xs"
                    >
                      {unreadNotifications > 9 ? '9+' : unreadNotifications}
                    </Badge>
                  </motion.div>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80 glass border-white/20">
              <div className="p-4 border-b border-white/10">
                <h3 className="font-semibold text-gray-900">Notifications</h3>
                <p className="text-sm text-gray-600">
                  {unreadNotifications > 0 
                    ? `${unreadNotifications} unread notification${unreadNotifications !== 1 ? 's' : ''}`
                    : 'All caught up!'
                  }
                </p>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {ui.notifications.length > 0 ? (
                  ui.notifications.slice(0, 5).map((notification) => (
                    <DropdownMenuItem 
                      key={notification.id}
                      className={cn(
                        'p-4 cursor-pointer',
                        !notification.read && 'bg-blue-50/50'
                      )}
                    >
                      <div className="space-y-1">
                        <p className="font-medium text-sm">{notification.title}</p>
                        <p className="text-xs text-gray-600">{notification.message}</p>
                        <p className="text-xs text-gray-400">
                          {notification.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </DropdownMenuItem>
                  ))
                ) : (
                  <div className="p-8 text-center text-gray-500">
                    <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No notifications yet</p>
                  </div>
                )}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
