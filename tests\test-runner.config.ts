/**
 * Test Runner Configuration
 * Centralized configuration for test execution and reporting
 */

export interface TestConfig {
  // Environment settings
  baseURL: string;
  headless: boolean;
  slowMo: number;
  
  // Test execution
  timeout: number;
  retries: number;
  workers: number;
  
  // Reporting
  outputDir: string;
  reportDir: string;
  
  // Browser settings
  browsers: string[];
  viewports: { name: string; width: number; height: number }[];
  
  // Test categories
  testSuites: {
    booking: string[];
    dashboard: string[];
    integration: string[];
    audit: string[];
    mobile: string[];
  };
}

export const testConfig: TestConfig = {
  // Environment
  baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000',
  headless: process.env.CI === 'true',
  slowMo: process.env.CI === 'true' ? 0 : 100,
  
  // Execution
  timeout: 60000, // 1 minute per test
  retries: process.env.CI === 'true' ? 2 : 0,
  workers: process.env.CI === 'true' ? 1 : 4,
  
  // Output
  outputDir: 'test-results',
  reportDir: 'test-results/reports',
  
  // Browsers
  browsers: ['chromium', 'firefox', 'webkit'],
  
  // Responsive breakpoints (as specified in requirements)
  viewports: [
    { name: 'mobile-320', width: 320, height: 568 },
    { name: 'mobile-375', width: 375, height: 812 },
    { name: 'mobile-414', width: 414, height: 896 },
    { name: 'tablet-768', width: 768, height: 1024 },
    { name: 'desktop-1024', width: 1024, height: 768 },
    { name: 'desktop-1440', width: 1440, height: 900 },
  ],
  
  // Test suites
  testSuites: {
    booking: [
      'tests/booking/booking-form.spec.ts',
      'tests/booking/booking-form-mobile.spec.ts'
    ],
    dashboard: [
      'tests/dashboard/authentication.spec.ts',
      'tests/dashboard/dashboard-functionality.spec.ts'
    ],
    integration: [
      'tests/integration/database.spec.ts',
      'tests/integration/external-services.spec.ts'
    ],
    audit: [
      'tests/audit/performance.spec.ts',
      'tests/audit/accessibility.spec.ts',
      'tests/audit/seo-security.spec.ts'
    ],
    mobile: [
      'tests/booking/booking-form-mobile.spec.ts'
    ]
  }
};

// Test execution helpers
export const getTestCommand = (suite: keyof TestConfig['testSuites'], options: Partial<{
  headed: boolean;
  debug: boolean;
  browser: string;
  grep: string;
}> = {}) => {
  const baseCommand = 'npx playwright test';
  const files = testConfig.testSuites[suite].join(' ');
  
  let command = `${baseCommand} ${files}`;
  
  if (options.headed) command += ' --headed';
  if (options.debug) command += ' --debug';
  if (options.browser) command += ` --project=${options.browser}`;
  if (options.grep) command += ` --grep="${options.grep}"`;
  
  return command;
};

// Performance thresholds
export const performanceThresholds = {
  pageLoad: 3000, // 3 seconds
  formSubmission: 5000, // 5 seconds
  apiResponse: 1000, // 1 second
  imageLoad: 2000, // 2 seconds
  firstContentfulPaint: 2000, // 2 seconds
  largestContentfulPaint: 4000, // 4 seconds
  cumulativeLayoutShift: 0.1, // CLS score
  firstInputDelay: 100, // 100ms
};

// Accessibility standards
export const accessibilityConfig = {
  standard: 'WCAG21AA',
  rules: {
    'color-contrast': { enabled: true },
    'keyboard-navigation': { enabled: true },
    'focus-indicators': { enabled: true },
    'alt-text': { enabled: true },
    'heading-hierarchy': { enabled: true },
    'form-labels': { enabled: true },
    'aria-attributes': { enabled: true },
  },
  excludeSelectors: [
    '.third-party-widget',
    '[data-test-ignore-a11y]'
  ]
};

// Security test configuration
export const securityConfig = {
  xssPayloads: [
    '<script>alert("XSS")</script>',
    '"><script>alert("XSS")</script>',
    'javascript:alert("XSS")',
    '<img src=x onerror=alert("XSS")>',
    '<svg onload=alert("XSS")>',
  ],
  sqlInjectionPayloads: [
    "'; DROP TABLE bookings; --",
    "' OR '1'='1",
    "admin'--",
    "' UNION SELECT * FROM users --",
    "1' AND (SELECT COUNT(*) FROM users) > 0 --",
  ],
  requiredSecurityHeaders: [
    'content-security-policy',
    'x-frame-options',
    'x-content-type-options',
    'referrer-policy',
  ],
  sensitiveDataPatterns: [
    /password/i,
    /secret/i,
    /private_key/i,
    /api_key/i,
    /database_url/i,
    /auth_token/i,
  ]
};

// Test data configuration
export const testDataConfig = {
  photographer: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    businessName: 'Test Photography Studio',
    ownerName: 'Test Photographer',
    phone: '+***********',
    whatsapp: '+***********',
  },
  booking: {
    defaultEventTypes: ['engagement', 'wedding', 'homecoming', 'wedding-homecoming', 'triple-combo'],
    defaultPackages: ['Basic', 'Premium', 'Luxury'],
    testLocations: ['Colombo', 'Kandy', 'Galle', 'Negombo', 'Ella'],
    futureDate: () => {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date.toISOString().split('T')[0];
    },
  }
};

// CI/CD integration
export const ciConfig = {
  github: {
    reporter: 'github',
    artifactPaths: [
      'test-results/',
      'playwright-report/',
    ],
    failureScreenshots: true,
    videoOnFailure: true,
  },
  slack: {
    webhook: process.env.SLACK_WEBHOOK_URL,
    channel: '#test-results',
    onFailure: true,
    onSuccess: false,
  },
  email: {
    smtp: {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      user: process.env.SMTP_USER,
      password: process.env.SMTP_PASSWORD,
    },
    recipients: ['<EMAIL>'],
    onFailure: true,
  }
};

export default testConfig;
