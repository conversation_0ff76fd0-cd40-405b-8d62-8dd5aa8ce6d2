import { test, expect } from '@playwright/test';
import { DashboardPage, BookingFormPage, generateBookingData } from '../utils/test-helpers';

/**
 * Dashboard Functionality Tests
 * Tests booking management, client management, and real-time notifications
 */

test.describe('Dashboard Overview', () => {
  test.beforeEach(async ({ page }) => {
    const dashboardPage = new DashboardPage(page);
    await dashboardPage.login(
      process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>',
      process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!'
    );
  });

  test('should display dashboard statistics @desktop', async ({ page }) => {
    await test.step('Verify dashboard stats are visible', async () => {
      await expect(page.locator('[data-testid="total-bookings"]')).toBeVisible();
      await expect(page.locator('[data-testid="pending-bookings"]')).toBeVisible();
      await expect(page.locator('[data-testid="confirmed-bookings"]')).toBeVisible();
      await expect(page.locator('[data-testid="total-revenue"]')).toBeVisible();
    });

    await test.step('Verify stats have numeric values', async () => {
      const totalBookings = await page.locator('[data-testid="total-bookings"] .stat-value').textContent();
      const pendingBookings = await page.locator('[data-testid="pending-bookings"] .stat-value').textContent();
      
      expect(totalBookings).toMatch(/^\d+$/);
      expect(pendingBookings).toMatch(/^\d+$/);
    });
  });

  test('should display recent bookings @desktop', async ({ page }) => {
    await test.step('Verify recent bookings section', async () => {
      await expect(page.locator('[data-testid="recent-bookings"]')).toBeVisible();
      await expect(page.locator('[data-testid="recent-bookings-title"]')).toContainText('Recent Bookings');
    });

    await test.step('Check booking items display', async () => {
      const bookingItems = page.locator('[data-testid="booking-item"]');
      const count = await bookingItems.count();
      
      if (count > 0) {
        // Verify first booking item has required fields
        const firstBooking = bookingItems.first();
        await expect(firstBooking.locator('[data-testid="client-name"]')).toBeVisible();
        await expect(firstBooking.locator('[data-testid="event-type"]')).toBeVisible();
        await expect(firstBooking.locator('[data-testid="booking-status"]')).toBeVisible();
        await expect(firstBooking.locator('[data-testid="event-date"]')).toBeVisible();
      }
    });
  });

  test('should display analytics charts @desktop', async ({ page }) => {
    await test.step('Verify analytics section', async () => {
      await expect(page.locator('[data-testid="analytics-section"]')).toBeVisible();
    });

    await test.step('Check chart components', async () => {
      await expect(page.locator('[data-testid="bookings-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="revenue-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="event-types-chart"]')).toBeVisible();
    });
  });

  test('should navigate between dashboard sections @desktop', async ({ page }) => {
    await test.step('Navigate to bookings', async () => {
      await page.click('[data-testid="nav-bookings"]');
      await expect(page).toHaveURL('/dashboard/bookings');
      await expect(page.locator('[data-testid="bookings-page-title"]')).toContainText('Bookings');
    });

    await test.step('Navigate to clients', async () => {
      await page.click('[data-testid="nav-clients"]');
      await expect(page).toHaveURL('/dashboard/clients');
      await expect(page.locator('[data-testid="clients-page-title"]')).toContainText('Clients');
    });

    await test.step('Navigate to analytics', async () => {
      await page.click('[data-testid="nav-analytics"]');
      await expect(page).toHaveURL('/dashboard/analytics');
      await expect(page.locator('[data-testid="analytics-page-title"]')).toContainText('Analytics');
    });

    await test.step('Navigate back to dashboard', async () => {
      await page.click('[data-testid="nav-dashboard"]');
      await expect(page).toHaveURL('/dashboard');
      await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
    });
  });
});

test.describe('Booking Management', () => {
  test.beforeEach(async ({ page }) => {
    const dashboardPage = new DashboardPage(page);
    await dashboardPage.login(
      process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>',
      process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!'
    );
    await dashboardPage.navigateToBookings();
  });

  test('should display bookings list @desktop', async ({ page }) => {
    await test.step('Verify bookings page layout', async () => {
      await expect(page.locator('[data-testid="bookings-table"]')).toBeVisible();
      await expect(page.locator('[data-testid="bookings-filters"]')).toBeVisible();
      await expect(page.locator('[data-testid="search-bookings"]')).toBeVisible();
    });

    await test.step('Check table headers', async () => {
      await expect(page.locator('[data-testid="header-reference"]')).toContainText('Reference');
      await expect(page.locator('[data-testid="header-client"]')).toContainText('Client');
      await expect(page.locator('[data-testid="header-event-type"]')).toContainText('Event Type');
      await expect(page.locator('[data-testid="header-date"]')).toContainText('Date');
      await expect(page.locator('[data-testid="header-status"]')).toContainText('Status');
      await expect(page.locator('[data-testid="header-actions"]')).toContainText('Actions');
    });
  });

  test('should filter bookings by status @desktop', async ({ page }) => {
    await test.step('Filter by pending status', async () => {
      await page.selectOption('[data-testid="status-filter"]', 'pending');
      await page.waitForTimeout(1000); // Allow filter to apply
    });

    await test.step('Verify filtered results', async () => {
      const statusCells = page.locator('[data-testid="booking-status"]');
      const count = await statusCells.count();
      
      if (count > 0) {
        for (let i = 0; i < count; i++) {
          const status = await statusCells.nth(i).textContent();
          expect(status?.toLowerCase()).toContain('pending');
        }
      }
    });

    await test.step('Filter by confirmed status', async () => {
      await page.selectOption('[data-testid="status-filter"]', 'confirmed');
      await page.waitForTimeout(1000);
    });

    await test.step('Clear filters', async () => {
      await page.selectOption('[data-testid="status-filter"]', '');
      await page.waitForTimeout(1000);
    });
  });

  test('should search bookings @desktop', async ({ page }) => {
    await test.step('Search by client name', async () => {
      await page.fill('[data-testid="search-bookings"]', 'John');
      await page.waitForTimeout(1000);
    });

    await test.step('Verify search results', async () => {
      const clientNames = page.locator('[data-testid="client-name"]');
      const count = await clientNames.count();
      
      if (count > 0) {
        const firstName = await clientNames.first().textContent();
        expect(firstName?.toLowerCase()).toContain('john');
      }
    });

    await test.step('Search by booking reference', async () => {
      await page.fill('[data-testid="search-bookings"]', 'BKG-');
      await page.waitForTimeout(1000);
    });

    await test.step('Clear search', async () => {
      await page.fill('[data-testid="search-bookings"]', '');
      await page.waitForTimeout(1000);
    });
  });

  test('should update booking status @desktop', async ({ page }) => {
    const bookingRow = page.locator('[data-testid="booking-item"]').first();
    
    await test.step('Open booking details', async () => {
      await bookingRow.click();
      await expect(page.locator('[data-testid="booking-details-modal"]')).toBeVisible();
    });

    await test.step('Update booking status', async () => {
      await page.selectOption('[data-testid="booking-status-select"]', 'confirmed');
      await page.fill('[data-testid="photographer-notes"]', 'Booking confirmed via phone call');
      await page.click('[data-testid="save-booking"]');
    });

    await test.step('Verify status update', async () => {
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Booking updated successfully');
      await page.click('[data-testid="close-modal"]');
      
      // Verify status in table
      await expect(bookingRow.locator('[data-testid="booking-status"]')).toContainText('confirmed');
    });
  });

  test('should view booking details @desktop', async ({ page }) => {
    const bookingRow = page.locator('[data-testid="booking-item"]').first();
    
    await test.step('Open booking details', async () => {
      await bookingRow.click();
      await expect(page.locator('[data-testid="booking-details-modal"]')).toBeVisible();
    });

    await test.step('Verify booking information displayed', async () => {
      await expect(page.locator('[data-testid="booking-reference"]')).toBeVisible();
      await expect(page.locator('[data-testid="client-info"]')).toBeVisible();
      await expect(page.locator('[data-testid="event-details"]')).toBeVisible();
      await expect(page.locator('[data-testid="package-info"]')).toBeVisible();
      await expect(page.locator('[data-testid="form-data"]')).toBeVisible();
    });

    await test.step('Verify action buttons', async () => {
      await expect(page.locator('[data-testid="email-client"]')).toBeVisible();
      await expect(page.locator('[data-testid="whatsapp-client"]')).toBeVisible();
      await expect(page.locator('[data-testid="download-pdf"]')).toBeVisible();
    });
  });

  test('should delete booking @desktop', async ({ page }) => {
    const bookingRow = page.locator('[data-testid="booking-item"]').first();
    const bookingReference = await bookingRow.locator('[data-testid="booking-reference"]').textContent();
    
    await test.step('Open booking actions menu', async () => {
      await bookingRow.locator('[data-testid="booking-actions"]').click();
      await page.click('[data-testid="delete-booking"]');
    });

    await test.step('Confirm deletion', async () => {
      await expect(page.locator('[data-testid="delete-confirmation"]')).toBeVisible();
      await expect(page.locator('[data-testid="delete-confirmation"]')).toContainText(bookingReference || '');
      await page.click('[data-testid="confirm-delete"]');
    });

    await test.step('Verify booking deleted', async () => {
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Booking deleted successfully');
      
      // Verify booking no longer appears in list
      const remainingBookings = page.locator('[data-testid="booking-reference"]');
      const count = await remainingBookings.count();
      
      for (let i = 0; i < count; i++) {
        const ref = await remainingBookings.nth(i).textContent();
        expect(ref).not.toBe(bookingReference);
      }
    });
  });
});

test.describe('Real-time Notifications', () => {
  test('should receive real-time booking notifications @desktop', async ({ page, context }) => {
    // Set up dashboard page
    const dashboardPage = new DashboardPage(page);
    await dashboardPage.login(
      process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>',
      process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!'
    );

    // Get initial booking count
    const initialCount = await dashboardPage.getBookingCount();

    await test.step('Submit new booking from separate context', async () => {
      // Create new booking in separate page (simulating external user)
      const bookingPage = await context.newPage();
      const bookingForm = new BookingFormPage(bookingPage);
      const bookingData = generateBookingData('engagement');

      await bookingForm.goto();
      await bookingForm.fillStep1('engagement');
      await bookingForm.fillStep2(bookingData.package);
      
      await bookingPage.fill('[data-testid="event-date"]', bookingData.eventDate);
      await bookingPage.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
      await bookingPage.click('[data-testid="next-step"]');
      
      await bookingForm.fillStep4(bookingData);
      await bookingForm.submitForm();
      await bookingForm.waitForSuccessMessage();
      
      await bookingPage.close();
    });

    await test.step('Verify real-time notification received', async () => {
      // Wait for real-time notification
      await dashboardPage.waitForNewBookingNotification();
      
      // Verify notification appears
      await expect(page.locator('[data-testid="booking-notification"]')).toBeVisible();
      await expect(page.locator('[data-testid="notification-title"]')).toContainText('New Booking');
      await expect(page.locator('[data-testid="notification-message"]')).toContainText('engagement booking');
    });

    await test.step('Verify booking count updated', async () => {
      const newCount = await dashboardPage.getBookingCount();
      expect(newCount).toBe(initialCount + 1);
    });

    await test.step('Verify booking appears in recent bookings', async () => {
      const latestBooking = await dashboardPage.getLatestBooking();
      expect(latestBooking.eventType).toContain('engagement');
      expect(latestBooking.status).toBe('pending');
    });
  });

  test('should display notification history @desktop', async ({ page }) => {
    const dashboardPage = new DashboardPage(page);
    await dashboardPage.login(
      process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>',
      process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!'
    );

    await test.step('Open notifications panel', async () => {
      await page.click('[data-testid="notifications-button"]');
      await expect(page.locator('[data-testid="notifications-panel"]')).toBeVisible();
    });

    await test.step('Verify notification list', async () => {
      const notifications = page.locator('[data-testid="notification-item"]');
      const count = await notifications.count();
      
      if (count > 0) {
        // Verify first notification structure
        const firstNotification = notifications.first();
        await expect(firstNotification.locator('[data-testid="notification-type"]')).toBeVisible();
        await expect(firstNotification.locator('[data-testid="notification-time"]')).toBeVisible();
        await expect(firstNotification.locator('[data-testid="notification-message"]')).toBeVisible();
      }
    });

    await test.step('Mark notification as read', async () => {
      const unreadNotification = page.locator('[data-testid="notification-item"][data-read="false"]').first();
      
      if (await unreadNotification.isVisible()) {
        await unreadNotification.click();
        await expect(unreadNotification).toHaveAttribute('data-read', 'true');
      }
    });

    await test.step('Clear all notifications', async () => {
      await page.click('[data-testid="clear-notifications"]');
      await expect(page.locator('[data-testid="no-notifications"]')).toContainText('No notifications');
    });
  });

  test('should handle connection status @desktop', async ({ page }) => {
    const dashboardPage = new DashboardPage(page);
    await dashboardPage.login(
      process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>',
      process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!'
    );

    await test.step('Verify connection status indicator', async () => {
      await expect(page.locator('[data-testid="connection-status"]')).toBeVisible();
      await expect(page.locator('[data-testid="connection-status"]')).toContainText('Connected');
    });

    await test.step('Simulate connection loss', async () => {
      // Block network requests to simulate connection loss
      await page.route('**/supabase.co/**', (route) => {
        route.abort('failed');
      });
      
      // Wait for connection status to update
      await page.waitForTimeout(5000);
    });

    await test.step('Verify disconnected status', async () => {
      await expect(page.locator('[data-testid="connection-status"]')).toContainText('Disconnected');
      await expect(page.locator('[data-testid="connection-warning"]')).toBeVisible();
    });

    await test.step('Restore connection', async () => {
      // Remove network blocking
      await page.unroute('**/supabase.co/**');
      
      // Wait for reconnection
      await page.waitForTimeout(5000);
    });

    await test.step('Verify reconnected status', async () => {
      await expect(page.locator('[data-testid="connection-status"]')).toContainText('Connected');
      await expect(page.locator('[data-testid="connection-warning"]')).not.toBeVisible();
    });
  });
});
