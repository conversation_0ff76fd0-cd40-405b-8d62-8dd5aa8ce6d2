import { test, expect } from '@playwright/test';
import { measurePageLoad, measureFormSubmission, BookingFormPage, generateBookingData, checkMobileResponsiveness } from '../utils/test-helpers';

/**
 * Performance Audit Tests
 * Tests page load times, form submission speed, and performance metrics
 */

test.describe('Page Load Performance', () => {
  const performanceThresholds = {
    pageLoad: 3000, // 3 seconds
    formSubmission: 5000, // 5 seconds
    imageLoad: 2000, // 2 seconds
    apiResponse: 1000 // 1 second
  };

  test('should load booking form within performance threshold @desktop', async ({ page }) => {
    await test.step('Measure booking form load time', async () => {
      const loadTime = await measurePageLoad(page, '/');
      console.log(`Booking form load time: ${loadTime}ms`);
      expect(loadTime).toBeLessThan(performanceThresholds.pageLoad);
    });

    await test.step('Verify critical resources loaded', async () => {
      // Check if critical CSS is loaded
      const styles = await page.locator('link[rel="stylesheet"]').count();
      expect(styles).toBeGreaterThan(0);

      // Check if JavaScript is loaded
      const scripts = await page.locator('script[src]').count();
      expect(scripts).toBeGreaterThan(0);

      // Verify form is interactive
      await expect(page.locator('[data-testid="event-type-engagement"]')).toBeVisible();
    });
  });

  test('should load dashboard within performance threshold @desktop', async ({ page }) => {
    await test.step('Login and measure dashboard load', async () => {
      // Login first
      await page.goto('/dashboard/login');
      await page.fill('[data-testid="email"]', process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>');
      await page.fill('[data-testid="password"]', process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!');
      await page.click('[data-testid="login-button"]');
      
      // Measure dashboard load time
      const loadTime = await measurePageLoad(page, '/dashboard');
      console.log(`Dashboard load time: ${loadTime}ms`);
      expect(loadTime).toBeLessThan(performanceThresholds.pageLoad);
    });

    await test.step('Verify dashboard components loaded', async () => {
      await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
      await expect(page.locator('[data-testid="booking-stats"]')).toBeVisible();
      await expect(page.locator('[data-testid="recent-bookings"]')).toBeVisible();
    });
  });

  test('should handle concurrent page loads efficiently @desktop', async ({ page, context }) => {
    const pages = await Promise.all([
      context.newPage(),
      context.newPage(),
      context.newPage()
    ]);

    await test.step('Load multiple pages concurrently', async () => {
      const loadPromises = pages.map(async (p, index) => {
        const startTime = Date.now();
        await p.goto('/');
        await p.waitForLoadState('networkidle');
        const endTime = Date.now();
        return { page: index, loadTime: endTime - startTime };
      });

      const results = await Promise.all(loadPromises);
      
      results.forEach(result => {
        console.log(`Page ${result.page} load time: ${result.loadTime}ms`);
        expect(result.loadTime).toBeLessThan(performanceThresholds.pageLoad * 1.5); // Allow 50% more time for concurrent loads
      });
    });

    // Clean up
    await Promise.all(pages.map(p => p.close()));
  });

  test('should optimize image loading @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Measure image load performance', async () => {
      const images = page.locator('img');
      const imageCount = await images.count();

      if (imageCount > 0) {
        // Wait for all images to load
        const startTime = Date.now();
        await page.waitForLoadState('networkidle');
        const endTime = Date.now();
        
        const imageLoadTime = endTime - startTime;
        console.log(`Images load time: ${imageLoadTime}ms`);
        expect(imageLoadTime).toBeLessThan(performanceThresholds.imageLoad);
      }
    });

    await test.step('Verify lazy loading implementation', async () => {
      const lazyImages = page.locator('img[loading="lazy"]');
      const lazyCount = await lazyImages.count();
      
      // Should have lazy loading for non-critical images
      if (await page.locator('img').count() > 3) {
        expect(lazyCount).toBeGreaterThan(0);
      }
    });
  });
});

test.describe('Form Submission Performance', () => {
  test('should submit engagement booking within threshold @desktop', async ({ page }) => {
    const bookingData = generateBookingData('engagement');
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Fill form and measure submission time', async () => {
      // Fill form
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);

      // Measure submission time
      const submissionTime = await measureFormSubmission(page, async () => {
        await bookingPage.submitForm();
        await bookingPage.waitForSuccessMessage();
      });

      console.log(`Form submission time: ${submissionTime}ms`);
      expect(submissionTime).toBeLessThan(5000); // 5 seconds threshold
    });
  });

  test('should handle large form data efficiently @desktop', async ({ page }) => {
    const bookingData = generateBookingData('triple-combo');
    // Add large additional notes to test performance with large data
    bookingData.additionalNotes = 'A'.repeat(1000); // 1KB of text
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Submit large form data', async () => {
      await bookingPage.fillStep1('triple-combo');
      await bookingPage.fillStep2(bookingData.package);
      
      // Fill all event details
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="wedding-date"]', bookingData.weddingDate);
      await page.fill('[data-testid="homecoming-date"]', bookingData.homecomingDate);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);

      const submissionTime = await measureFormSubmission(page, async () => {
        await bookingPage.submitForm();
        await bookingPage.waitForSuccessMessage();
      });

      console.log(`Large form submission time: ${submissionTime}ms`);
      expect(submissionTime).toBeLessThan(7000); // Allow more time for large data
    });
  });

  test('should optimize API response times @desktop', async ({ page }) => {
    let apiResponseTimes: number[] = [];

    // Intercept API calls to measure response times
    await page.route('**/supabase.co/**', async (route) => {
      const startTime = Date.now();
      await route.continue();
      const endTime = Date.now();
      apiResponseTimes.push(endTime - startTime);
    });

    const bookingData = generateBookingData('wedding');
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Submit booking and measure API performance', async () => {
      await bookingPage.fillStep1('wedding');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify API response times', async () => {
      expect(apiResponseTimes.length).toBeGreaterThan(0);
      
      const averageResponseTime = apiResponseTimes.reduce((a, b) => a + b, 0) / apiResponseTimes.length;
      console.log(`Average API response time: ${averageResponseTime}ms`);
      console.log(`API response times:`, apiResponseTimes);
      
      // Most API calls should be under 1 second
      const fastResponses = apiResponseTimes.filter(time => time < 1000);
      expect(fastResponses.length / apiResponseTimes.length).toBeGreaterThan(0.8); // 80% should be fast
    });
  });
});

test.describe('Mobile Performance', () => {
  const mobileBreakpoints = [320, 375, 414, 768];

  for (const width of mobileBreakpoints) {
    test(`should perform well on ${width}px viewport @mobile`, async ({ page }) => {
      await page.setViewportSize({ width, height: 800 });

      await test.step(`Measure load time on ${width}px`, async () => {
        const loadTime = await measurePageLoad(page, '/');
        console.log(`${width}px load time: ${loadTime}ms`);
        
        // Mobile should load within 4 seconds (slightly more lenient)
        expect(loadTime).toBeLessThan(4000);
      });

      await test.step(`Test form interaction on ${width}px`, async () => {
        const bookingData = generateBookingData('engagement');
        
        // Test basic form interaction
        await page.click('[data-testid="event-type-engagement"]');
        await page.click('[data-testid="next-step"]');
        
        // Measure interaction responsiveness
        const startTime = Date.now();
        await page.click('[data-testid="package-basic-engagement"]');
        await page.waitForSelector('[data-testid="package-basic-engagement"][data-selected="true"]');
        const interactionTime = Date.now() - startTime;
        
        console.log(`${width}px interaction time: ${interactionTime}ms`);
        expect(interactionTime).toBeLessThan(500); // Should be very responsive
      });
    });
  }

  test('should handle touch gestures efficiently @mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 812 });
    await page.goto('/');

    await test.step('Test touch responsiveness', async () => {
      const eventTypeCard = page.locator('[data-testid="event-type-engagement"]');
      
      // Measure touch response time
      const startTime = Date.now();
      await eventTypeCard.tap();
      await page.waitForSelector('[data-testid="event-type-engagement"][data-selected="true"]');
      const touchResponseTime = Date.now() - startTime;
      
      console.log(`Touch response time: ${touchResponseTime}ms`);
      expect(touchResponseTime).toBeLessThan(300); // Should be very fast
    });

    await test.step('Test scroll performance', async () => {
      // Navigate to a page with scrollable content
      await page.click('[data-testid="next-step"]');
      
      // Measure scroll performance
      const startTime = Date.now();
      await page.mouse.wheel(0, 500);
      await page.waitForTimeout(100); // Allow scroll to complete
      const scrollTime = Date.now() - startTime;
      
      console.log(`Scroll performance: ${scrollTime}ms`);
      expect(scrollTime).toBeLessThan(200);
    });
  });
});

test.describe('Resource Optimization', () => {
  test('should optimize bundle sizes @desktop', async ({ page }) => {
    const resourceSizes: { [key: string]: number } = {};

    // Intercept network requests to measure resource sizes
    await page.route('**/*', (route) => {
      const url = route.request().url();
      const resourceType = route.request().resourceType();
      
      route.continue().then(() => {
        // This would need to be implemented with actual response size measurement
        // For now, we'll just track the requests
        if (!resourceSizes[resourceType]) {
          resourceSizes[resourceType] = 0;
        }
        resourceSizes[resourceType]++;
      });
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    await test.step('Verify resource optimization', async () => {
      console.log('Resource counts by type:', resourceSizes);
      
      // Should not have excessive resources
      expect(resourceSizes.script || 0).toBeLessThan(20);
      expect(resourceSizes.stylesheet || 0).toBeLessThan(10);
      expect(resourceSizes.image || 0).toBeLessThan(30);
    });
  });

  test('should implement caching strategies @desktop', async ({ page }) => {
    await test.step('Check cache headers', async () => {
      const response = await page.goto('/');
      const cacheControl = response?.headers()['cache-control'];
      
      // Should have appropriate cache headers for static assets
      if (cacheControl) {
        console.log('Cache-Control header:', cacheControl);
      }
    });

    await test.step('Verify static asset caching', async () => {
      // Load page twice to test caching
      await page.goto('/');
      const firstLoadTime = await measurePageLoad(page, '/');
      
      // Second load should be faster due to caching
      const secondLoadTime = await measurePageLoad(page, '/');
      
      console.log(`First load: ${firstLoadTime}ms, Second load: ${secondLoadTime}ms`);
      expect(secondLoadTime).toBeLessThan(firstLoadTime * 0.8); // Should be at least 20% faster
    });
  });

  test('should minimize render blocking @desktop', async ({ page }) => {
    await page.goto('/');

    await test.step('Check for render blocking resources', async () => {
      // Measure time to first contentful paint
      const metrics = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const fcp = entries.find(entry => entry.name === 'first-contentful-paint');
            if (fcp) {
              resolve(fcp.startTime);
            }
          }).observe({ entryTypes: ['paint'] });
        });
      });

      console.log('First Contentful Paint:', metrics);
      expect(metrics).toBeLessThan(2000); // Should render content within 2 seconds
    });

    await test.step('Verify critical CSS inlined', async () => {
      const inlineStyles = await page.locator('style').count();
      expect(inlineStyles).toBeGreaterThan(0); // Should have some critical CSS inlined
    });
  });
});
