import { test, expect } from '@playwright/test';
import { DashboardPage } from '../utils/test-helpers';

/**
 * Dashboard Authentication Tests
 * Tests login, logout, session management, and authentication flows
 */

test.describe('Authentication Flow', () => {
  test('should login photographer successfully @desktop', async ({ page }) => {
    const dashboardPage = new DashboardPage(page);

    await test.step('Navigate to login page', async () => {
      await page.goto('/dashboard/login');
      await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
    });

    await test.step('Login with valid credentials', async () => {
      await page.fill('[data-testid="email"]', process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>');
      await page.fill('[data-testid="password"]', process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!');
      await page.click('[data-testid="login-button"]');
    });

    await test.step('Verify successful login', async () => {
      await expect(page).toHaveURL('/dashboard');
      await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
      await expect(page.locator('[data-testid="photographer-name"]')).toBeVisible();
    });

    await test.step('Verify dashboard components loaded', async () => {
      await expect(page.locator('[data-testid="booking-stats"]')).toBeVisible();
      await expect(page.locator('[data-testid="recent-bookings"]')).toBeVisible();
      await expect(page.locator('[data-testid="navigation-menu"]')).toBeVisible();
    });
  });

  test('should handle invalid login credentials @desktop', async ({ page }) => {
    await test.step('Navigate to login page', async () => {
      await page.goto('/dashboard/login');
    });

    await test.step('Attempt login with invalid credentials', async () => {
      await page.fill('[data-testid="email"]', '<EMAIL>');
      await page.fill('[data-testid="password"]', 'wrongpassword');
      await page.click('[data-testid="login-button"]');
    });

    await test.step('Verify error handling', async () => {
      await expect(page.locator('[data-testid="error-message"]')).toContainText('Invalid credentials');
      await expect(page).toHaveURL('/dashboard/login');
    });
  });

  test('should validate required login fields @desktop', async ({ page }) => {
    await test.step('Navigate to login page', async () => {
      await page.goto('/dashboard/login');
    });

    await test.step('Submit empty login form', async () => {
      await page.click('[data-testid="login-button"]');
    });

    await test.step('Verify validation errors', async () => {
      await expect(page.locator('[data-testid="email-error"]')).toContainText('Email is required');
      await expect(page.locator('[data-testid="password-error"]')).toContainText('Password is required');
    });
  });

  test('should logout photographer successfully @desktop', async ({ page }) => {
    const dashboardPage = new DashboardPage(page);

    await test.step('Login first', async () => {
      await dashboardPage.login(
        process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>',
        process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!'
      );
    });

    await test.step('Logout from dashboard', async () => {
      await page.click('[data-testid="user-menu"]');
      await page.click('[data-testid="logout-button"]');
    });

    await test.step('Verify successful logout', async () => {
      await expect(page).toHaveURL('/dashboard/login');
      await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
    });

    await test.step('Verify session cleared', async () => {
      // Try to access dashboard directly
      await page.goto('/dashboard');
      await expect(page).toHaveURL('/dashboard/login');
    });
  });

  test('should handle session expiration @desktop', async ({ page }) => {
    const dashboardPage = new DashboardPage(page);

    await test.step('Login and access dashboard', async () => {
      await dashboardPage.login(
        process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>',
        process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!'
      );
      await expect(page).toHaveURL('/dashboard');
    });

    await test.step('Simulate session expiration', async () => {
      // Clear session storage to simulate expiration
      await page.evaluate(() => {
        localStorage.clear();
        sessionStorage.clear();
      });
      
      // Clear cookies
      await page.context().clearCookies();
    });

    await test.step('Attempt to access protected route', async () => {
      await page.goto('/dashboard/bookings');
    });

    await test.step('Verify redirect to login', async () => {
      await expect(page).toHaveURL('/dashboard/login');
      await expect(page.locator('[data-testid="session-expired-message"]')).toBeVisible();
    });
  });

  test('should redirect authenticated user from login page @desktop', async ({ page }) => {
    const dashboardPage = new DashboardPage(page);

    await test.step('Login first', async () => {
      await dashboardPage.login(
        process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>',
        process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!'
      );
    });

    await test.step('Try to access login page while authenticated', async () => {
      await page.goto('/dashboard/login');
    });

    await test.step('Verify redirect to dashboard', async () => {
      await expect(page).toHaveURL('/dashboard');
      await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
    });
  });
});

test.describe('Authentication Security', () => {
  test('should protect dashboard routes @desktop', async ({ page }) => {
    const protectedRoutes = [
      '/dashboard',
      '/dashboard/bookings',
      '/dashboard/clients',
      '/dashboard/analytics',
      '/dashboard/settings'
    ];

    for (const route of protectedRoutes) {
      await test.step(`Test protection for ${route}`, async () => {
        await page.goto(route);
        await expect(page).toHaveURL('/dashboard/login');
      });
    }
  });

  test('should handle concurrent login attempts @desktop', async ({ page, context }) => {
    const page2 = await context.newPage();

    await test.step('Attempt concurrent logins', async () => {
      const loginPromise1 = page.goto('/dashboard/login').then(() => {
        return page.fill('[data-testid="email"]', process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>')
          .then(() => page.fill('[data-testid="password"]', process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!'))
          .then(() => page.click('[data-testid="login-button"]'));
      });

      const loginPromise2 = page2.goto('/dashboard/login').then(() => {
        return page2.fill('[data-testid="email"]', process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>')
          .then(() => page2.fill('[data-testid="password"]', process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!'))
          .then(() => page2.click('[data-testid="login-button"]'));
      });

      await Promise.all([loginPromise1, loginPromise2]);
    });

    await test.step('Verify both sessions are valid', async () => {
      await expect(page).toHaveURL('/dashboard');
      await expect(page2).toHaveURL('/dashboard');
      
      await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
      await expect(page2.locator('[data-testid="dashboard-header"]')).toBeVisible();
    });

    await page2.close();
  });

  test('should handle password reset flow @desktop', async ({ page }) => {
    await test.step('Navigate to login page', async () => {
      await page.goto('/dashboard/login');
    });

    await test.step('Click forgot password link', async () => {
      await page.click('[data-testid="forgot-password-link"]');
      await expect(page).toHaveURL('/dashboard/reset-password');
    });

    await test.step('Submit password reset request', async () => {
      await page.fill('[data-testid="reset-email"]', process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>');
      await page.click('[data-testid="reset-button"]');
    });

    await test.step('Verify reset confirmation', async () => {
      await expect(page.locator('[data-testid="reset-confirmation"]')).toContainText('Password reset email sent');
    });
  });

  test('should validate email format in login @desktop', async ({ page }) => {
    await test.step('Navigate to login page', async () => {
      await page.goto('/dashboard/login');
    });

    await test.step('Enter invalid email format', async () => {
      await page.fill('[data-testid="email"]', 'invalid-email');
      await page.fill('[data-testid="password"]', 'password123');
      await page.click('[data-testid="login-button"]');
    });

    await test.step('Verify email validation error', async () => {
      await expect(page.locator('[data-testid="email-error"]')).toContainText('Invalid email format');
    });
  });

  test('should enforce password requirements @desktop', async ({ page }) => {
    await test.step('Navigate to signup page', async () => {
      await page.goto('/dashboard/signup');
    });

    await test.step('Test weak password', async () => {
      await page.fill('[data-testid="email"]', '<EMAIL>');
      await page.fill('[data-testid="password"]', '123'); // Weak password
      await page.fill('[data-testid="business-name"]', 'Test Studio');
      await page.fill('[data-testid="owner-name"]', 'Test Owner');
      await page.click('[data-testid="signup-button"]');
    });

    await test.step('Verify password strength validation', async () => {
      await expect(page.locator('[data-testid="password-error"]')).toContainText('Password must be at least 8 characters');
    });
  });
});

test.describe('Profile Management', () => {
  test.beforeEach(async ({ page }) => {
    const dashboardPage = new DashboardPage(page);
    await dashboardPage.login(
      process.env.TEST_PHOTOGRAPHER_EMAIL || '<EMAIL>',
      process.env.TEST_PHOTOGRAPHER_PASSWORD || 'TestPassword123!'
    );
  });

  test('should display photographer profile information @desktop', async ({ page }) => {
    await test.step('Navigate to profile settings', async () => {
      await page.click('[data-testid="nav-settings"]');
      await expect(page).toHaveURL('/dashboard/settings');
    });

    await test.step('Verify profile information displayed', async () => {
      await expect(page.locator('[data-testid="business-name"]')).toBeVisible();
      await expect(page.locator('[data-testid="owner-name"]')).toBeVisible();
      await expect(page.locator('[data-testid="email"]')).toBeVisible();
      await expect(page.locator('[data-testid="phone"]')).toBeVisible();
    });
  });

  test('should update photographer profile @desktop', async ({ page }) => {
    await test.step('Navigate to profile settings', async () => {
      await page.click('[data-testid="nav-settings"]');
    });

    await test.step('Update profile information', async () => {
      await page.fill('[data-testid="business-name"]', 'Updated Photography Studio');
      await page.fill('[data-testid="phone"]', '+***********');
      await page.fill('[data-testid="bio"]', 'Professional wedding and engagement photographer');
      await page.click('[data-testid="save-profile"]');
    });

    await test.step('Verify profile update success', async () => {
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Profile updated successfully');
      
      // Verify updated values are displayed
      await expect(page.locator('[data-testid="business-name"]')).toHaveValue('Updated Photography Studio');
      await expect(page.locator('[data-testid="phone"]')).toHaveValue('+***********');
    });
  });

  test('should update EmailJS configuration @desktop', async ({ page }) => {
    await test.step('Navigate to integration settings', async () => {
      await page.click('[data-testid="nav-settings"]');
      await page.click('[data-testid="integrations-tab"]');
    });

    await test.step('Update EmailJS settings', async () => {
      await page.fill('[data-testid="emailjs-service-id"]', 'service_updated');
      await page.fill('[data-testid="emailjs-template-id"]', 'template_updated');
      await page.fill('[data-testid="emailjs-public-key"]', 'key_updated');
      await page.click('[data-testid="save-integrations"]');
    });

    await test.step('Verify integration update success', async () => {
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Integration settings updated');
    });

    await test.step('Test EmailJS configuration', async () => {
      await page.click('[data-testid="test-emailjs"]');
      await expect(page.locator('[data-testid="test-result"]')).toContainText('EmailJS test successful');
    });
  });

  test('should validate profile form fields @desktop', async ({ page }) => {
    await test.step('Navigate to profile settings', async () => {
      await page.click('[data-testid="nav-settings"]');
    });

    await test.step('Clear required fields and save', async () => {
      await page.fill('[data-testid="business-name"]', '');
      await page.fill('[data-testid="owner-name"]', '');
      await page.click('[data-testid="save-profile"]');
    });

    await test.step('Verify validation errors', async () => {
      await expect(page.locator('[data-testid="business-name-error"]')).toContainText('Business name is required');
      await expect(page.locator('[data-testid="owner-name-error"]')).toContainText('Owner name is required');
    });
  });
});
