'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import { 
  LayoutDashboard, 
  Calendar, 
  Users, 
  BarChart3, 
  Settings,
  Search,
  Plus,
  FileText,
  Phone,
  Mail
} from 'lucide-react';
import { useDashboardStore } from '@/lib/store/dashboard-store';
import { useBookings } from '@/lib/hooks/use-bookings';

interface CommandPaletteProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const navigationCommands = [
  {
    id: 'nav-dashboard',
    title: 'Dashboard Overview',
    subtitle: 'Go to main dashboard',
    icon: LayoutDashboard,
    action: '/dashboard',
  },
  {
    id: 'nav-bookings',
    title: 'Bookings',
    subtitle: 'Manage your bookings',
    icon: Calendar,
    action: '/dashboard/bookings',
  },
  {
    id: 'nav-clients',
    title: 'Clients',
    subtitle: 'View and manage clients',
    icon: Users,
    action: '/dashboard/clients',
  },
  {
    id: 'nav-analytics',
    title: 'Analytics',
    subtitle: 'Business insights and reports',
    icon: BarChart3,
    action: '/dashboard/analytics',
  },
  {
    id: 'nav-settings',
    title: 'Settings',
    subtitle: 'Account and preferences',
    icon: Settings,
    action: '/dashboard/settings',
  },
];

const actionCommands = [
  {
    id: 'action-new-booking',
    title: 'Create New Booking',
    subtitle: 'Add a new booking manually',
    icon: Plus,
    action: 'create-booking',
  },
  {
    id: 'action-new-client',
    title: 'Add New Client',
    subtitle: 'Add a client to your database',
    icon: Plus,
    action: 'create-client',
  },
  {
    id: 'action-export-data',
    title: 'Export Data',
    subtitle: 'Download your data as CSV',
    icon: FileText,
    action: 'export-data',
  },
];

export function CommandPalette({ open, onOpenChange }: CommandPaletteProps) {
  const router = useRouter();
  const { bookings, clients } = useDashboardStore();
  const { data: bookingsData } = useBookings();
  const [searchQuery, setSearchQuery] = useState('');

  // Reset search when dialog closes
  useEffect(() => {
    if (!open) {
      setSearchQuery('');
    }
  }, [open]);

  const handleCommand = (command: string) => {
    onOpenChange(false);
    
    if (command.startsWith('/')) {
      // Navigation command
      router.push(command);
    } else if (command.startsWith('booking-')) {
      // Booking command
      const bookingId = command.replace('booking-', '');
      router.push(`/dashboard/bookings?id=${bookingId}`);
    } else if (command.startsWith('client-')) {
      // Client command
      const clientId = command.replace('client-', '');
      router.push(`/dashboard/clients?id=${clientId}`);
    } else {
      // Action command
      switch (command) {
        case 'create-booking':
          router.push('/dashboard/bookings?action=create');
          break;
        case 'create-client':
          router.push('/dashboard/clients?action=create');
          break;
        case 'export-data':
          // Implement export functionality
          console.log('Exporting data...');
          break;
      }
    }
  };

  // Filter bookings and clients based on search query
  const filteredBookings = bookingsData?.filter(booking =>
    booking.client_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    booking.event_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
    booking.booking_reference.toLowerCase().includes(searchQuery.toLowerCase())
  ).slice(0, 5) || [];

  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    client.email?.toLowerCase().includes(searchQuery.toLowerCase())
  ).slice(0, 5);

  return (
    <CommandDialog open={open} onOpenChange={onOpenChange}>
      <Command className="rounded-lg border shadow-md">
        <CommandInput 
          placeholder="Search bookings, clients, or navigate..." 
          value={searchQuery}
          onValueChange={setSearchQuery}
        />
        <CommandList>
          <CommandEmpty>No results found.</CommandEmpty>
          
          {/* Navigation */}
          <CommandGroup heading="Navigation">
            {navigationCommands.map((command) => {
              const Icon = command.icon;
              return (
                <CommandItem
                  key={command.id}
                  value={command.title}
                  onSelect={() => handleCommand(command.action)}
                  className="flex items-center gap-3 p-3"
                >
                  <Icon className="w-4 h-4" />
                  <div>
                    <p className="font-medium">{command.title}</p>
                    <p className="text-sm text-gray-500">{command.subtitle}</p>
                  </div>
                </CommandItem>
              );
            })}
          </CommandGroup>

          <CommandSeparator />

          {/* Quick Actions */}
          <CommandGroup heading="Quick Actions">
            {actionCommands.map((command) => {
              const Icon = command.icon;
              return (
                <CommandItem
                  key={command.id}
                  value={command.title}
                  onSelect={() => handleCommand(command.action)}
                  className="flex items-center gap-3 p-3"
                >
                  <Icon className="w-4 h-4" />
                  <div>
                    <p className="font-medium">{command.title}</p>
                    <p className="text-sm text-gray-500">{command.subtitle}</p>
                  </div>
                </CommandItem>
              );
            })}
          </CommandGroup>

          {/* Bookings */}
          {filteredBookings.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup heading="Recent Bookings">
                {filteredBookings.map((booking) => (
                  <CommandItem
                    key={booking.id}
                    value={`${booking.client_name} ${booking.event_type} ${booking.booking_reference}`}
                    onSelect={() => handleCommand(`booking-${booking.id}`)}
                    className="flex items-center gap-3 p-3"
                  >
                    <Calendar className="w-4 h-4" />
                    <div className="flex-1">
                      <p className="font-medium">{booking.client_name}</p>
                      <p className="text-sm text-gray-500">
                        {booking.event_type} • {booking.booking_reference}
                      </p>
                    </div>
                    <div className="text-xs text-gray-400">
                      {booking.event_date && new Date(booking.event_date).toLocaleDateString()}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </>
          )}

          {/* Clients */}
          {filteredClients.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup heading="Clients">
                {filteredClients.map((client) => (
                  <CommandItem
                    key={client.id}
                    value={`${client.name} ${client.email || ''}`}
                    onSelect={() => handleCommand(`client-${client.id}`)}
                    className="flex items-center gap-3 p-3"
                  >
                    <Users className="w-4 h-4" />
                    <div className="flex-1">
                      <p className="font-medium">{client.name}</p>
                      <p className="text-sm text-gray-500">
                        {client.email || 'No email'}
                      </p>
                    </div>
                    <div className="text-xs text-gray-400">
                      {client.total_bookings} booking{client.total_bookings !== 1 ? 's' : ''}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </>
          )}
        </CommandList>
      </Command>
    </CommandDialog>
  );
}
