'use client';

import { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { useDashboardStore } from '@/lib/store/dashboard-store';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

const notificationIcons = {
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info,
};

const notificationStyles = {
  success: 'border-green-200 bg-green-50/90 text-green-800',
  error: 'border-red-200 bg-red-50/90 text-red-800',
  warning: 'border-amber-200 bg-amber-50/90 text-amber-800',
  info: 'border-blue-200 bg-blue-50/90 text-blue-800',
};

export function NotificationCenter() {
  const { ui, actions } = useDashboardStore();

  // Auto-dismiss notifications after 5 seconds
  useEffect(() => {
    const timers = ui.notifications.map((notification) => {
      if (!notification.read) {
        return setTimeout(() => {
          actions.markNotificationRead(notification.id);
        }, 5000);
      }
      return null;
    });

    return () => {
      timers.forEach((timer) => {
        if (timer) clearTimeout(timer);
      });
    };
  }, [ui.notifications, actions]);

  const visibleNotifications = ui.notifications
    .filter(n => !n.read)
    .slice(0, 3); // Show max 3 notifications

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 w-96">
      <AnimatePresence mode="popLayout">
        {visibleNotifications.map((notification) => {
          const Icon = notificationIcons[notification.type];
          
          return (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, x: 300, scale: 0.9 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: 300, scale: 0.9 }}
              transition={{ 
                type: 'spring', 
                stiffness: 300, 
                damping: 30 
              }}
              layout
              className={cn(
                'glass border backdrop-blur-xl rounded-lg p-4 shadow-lg',
                notificationStyles[notification.type]
              )}
            >
              <div className="flex items-start gap-3">
                <Icon className="w-5 h-5 mt-0.5 flex-shrink-0" />
                
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-sm mb-1">
                    {notification.title}
                  </h4>
                  <p className="text-sm opacity-90 leading-relaxed">
                    {notification.message}
                  </p>
                  <p className="text-xs opacity-70 mt-2">
                    {notification.timestamp.toLocaleTimeString()}
                  </p>
                </div>

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => actions.markNotificationRead(notification.id)}
                  className="h-6 w-6 hover:bg-black/10 flex-shrink-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {/* Progress bar for auto-dismiss */}
              <motion.div
                initial={{ width: '100%' }}
                animate={{ width: '0%' }}
                transition={{ duration: 5, ease: 'linear' }}
                className="absolute bottom-0 left-0 h-1 bg-current opacity-30 rounded-b-lg"
              />
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );
}
