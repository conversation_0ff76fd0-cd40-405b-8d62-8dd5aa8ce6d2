import { Page, expect } from '@playwright/test';
import { faker } from '@faker-js/faker';

/**
 * Test Utilities for Tera Works Photographer Booking System
 * Common functions and data generators for testing
 */

// Test data generators
interface BaseBookingData {
  eventType: string;
  brideAName: string;
  groomName: string;
  email: string;
  phoneNumber: string;
  additionalNotes: string;
  hearAbout: string;
  agreeTerms: boolean;
  package: string;
  eventDate?: string;
  desiredLocation?: string;
  outfitChanges?: string;
  venue?: string;
  registrationTime?: string;
  ringExchangeTime?: string;
  ceremonyTime?: string;
  eventEndTime?: string;
  guestCount?: string;
  makeupArtist?: string;
  homecomingDate?: string;
  homecomingVenue?: string;
  homecomingEndTime?: string;
  weddingDate?: string;
}

export const generateBookingData = (eventType: 'engagement' | 'wedding' | 'homecoming' | 'wedding-homecoming' | 'triple-combo' = 'engagement'): BaseBookingData => {
  const brideFirstName = faker.person.firstName('female');
  const groomFirstName = faker.person.firstName('male');
  const brideLastName = faker.person.lastName();
  const groomLastName = faker.person.lastName();
  
  const baseData = {
    eventType,
    brideAName: `${brideFirstName} ${brideLastName}`,
    groomName: `${groomFirstName} ${groomLastName}`,
    email: faker.internet.email(),
    phoneNumber: `+94${faker.string.numeric(9)}`,
    additionalNotes: faker.lorem.sentence(),
    hearAbout: faker.helpers.arrayElement(['Google Search', 'Facebook', 'Instagram', 'Friend Referral', 'Previous Client']),
    agreeTerms: true,
  };

  // Event-specific data
  switch (eventType) {
    case 'engagement':
      return {
        ...baseData,
        package: faker.helpers.arrayElement(['Basic Engagement', 'Premium Engagement', 'Luxury Engagement']),
        eventDate: faker.date.future().toISOString().split('T')[0],
        desiredLocation: faker.location.city(),
        outfitChanges: faker.helpers.arrayElement(['1', '2', '3', '4+']),
      };

    case 'wedding':
      return {
        ...baseData,
        package: faker.helpers.arrayElement(['Basic Wedding', 'Premium Wedding', 'Luxury Wedding']),
        eventDate: faker.date.future().toISOString().split('T')[0],
        venue: `${faker.company.name()} Hotel`,
        registrationTime: '08:00',
        ringExchangeTime: '10:00',
        ceremonyTime: '11:00',
        eventEndTime: '18:00',
        guestCount: faker.number.int({ min: 50, max: 500 }).toString(),
        makeupArtist: faker.person.fullName(),
      };

    case 'homecoming':
      return {
        ...baseData,
        package: faker.helpers.arrayElement(['Basic Homecoming', 'Premium Homecoming', 'Luxury Homecoming']),
        homecomingDate: faker.date.future().toISOString().split('T')[0],
        homecomingVenue: `${faker.company.name()} Hall`,
        homecomingEndTime: '22:00',
      };

    case 'wedding-homecoming':
      return {
        ...baseData,
        package: faker.helpers.arrayElement(['Dual Package Basic', 'Dual Package Premium', 'Dual Package Luxury']),
        weddingDate: faker.date.future().toISOString().split('T')[0],
        homecomingDate: faker.date.future().toISOString().split('T')[0],
        venue: `${faker.company.name()} Hotel`,
        homecomingVenue: `${faker.company.name()} Hall`,
        registrationTime: '08:00',
        ceremonyTime: '11:00',
        eventEndTime: '18:00',
        homecomingEndTime: '22:00',
        guestCount: faker.number.int({ min: 50, max: 500 }).toString(),
      };

    case 'triple-combo':
      return {
        ...baseData,
        package: faker.helpers.arrayElement(['Triple Combo Basic', 'Triple Combo Premium', 'Triple Combo Luxury']),
        eventDate: faker.date.future().toISOString().split('T')[0], // Engagement
        weddingDate: faker.date.future().toISOString().split('T')[0],
        homecomingDate: faker.date.future().toISOString().split('T')[0],
        desiredLocation: faker.location.city(),
        venue: `${faker.company.name()} Hotel`,
        homecomingVenue: `${faker.company.name()} Hall`,
        outfitChanges: faker.helpers.arrayElement(['2', '3', '4+']),
        registrationTime: '08:00',
        ceremonyTime: '11:00',
        eventEndTime: '18:00',
        homecomingEndTime: '22:00',
        guestCount: faker.number.int({ min: 50, max: 500 }).toString(),
      };

    default:
      return baseData;
  }
};

// Generate invalid test data for validation testing
export const generateInvalidBookingData = () => ({
  invalidEmail: {
    email: 'invalid-email',
    expectedError: 'Invalid email format'
  },
  invalidPhone: {
    phoneNumber: '123',
    expectedError: 'Invalid phone number format'
  },
  pastDate: {
    eventDate: '2020-01-01',
    expectedError: 'Event date must be in the future'
  },
  emptyRequired: {
    brideAName: '',
    email: '',
    phoneNumber: '',
    expectedErrors: ['Name is required', 'Email is required', 'Phone number is required']
  }
});

// Page object helpers
export class BookingFormPage {
  constructor(private page: Page) {}

  async goto() {
    await this.page.goto('/');
    await this.page.waitForLoadState('networkidle');
  }

  async fillStep1(eventType: string) {
    await this.page.click(`[data-testid="event-type-${eventType}"]`);
    await this.page.click('[data-testid="next-step"]');
  }

  async fillStep2(packageType: string) {
    await this.page.click(`[data-testid="package-${packageType.toLowerCase().replace(/\s+/g, '-')}"]`);
    await this.page.click('[data-testid="next-step"]');
  }

  async fillStep3(data: any) {
    // Fill event details based on event type
    if (data.eventDate) {
      await this.page.fill('[data-testid="event-date"]', data.eventDate);
    }
    if (data.venue) {
      await this.page.fill('[data-testid="venue"]', data.venue);
    }
    if (data.desiredLocation) {
      await this.page.fill('[data-testid="desired-location"]', data.desiredLocation);
    }
    // Add more fields as needed
    await this.page.click('[data-testid="next-step"]');
  }

  async fillStep4(data: any) {
    // Fill contact information
    await this.page.fill('[data-testid="bride-name"]', data.brideAName);
    if (data.groomName) {
      await this.page.fill('[data-testid="groom-name"]', data.groomName);
    }
    await this.page.fill('[data-testid="email"]', data.email);
    await this.page.fill('[data-testid="phone"]', data.phoneNumber);
    if (data.additionalNotes) {
      await this.page.fill('[data-testid="additional-notes"]', data.additionalNotes);
    }
    await this.page.selectOption('[data-testid="hear-about"]', data.hearAbout);
    await this.page.check('[data-testid="agree-terms"]');
  }

  async submitForm() {
    await this.page.click('[data-testid="submit-booking"]');
  }

  async waitForSuccessMessage() {
    await this.page.waitForSelector('[data-testid="success-message"]', { timeout: 30000 });
  }

  async getValidationErrors() {
    const errors = await this.page.locator('[data-testid*="error"]').allTextContents();
    return errors;
  }
}

export class DashboardPage {
  constructor(private page: Page) {}

  async goto() {
    await this.page.goto('/dashboard');
    await this.page.waitForLoadState('networkidle');
  }

  async login(email: string, password: string) {
    await this.page.goto('/dashboard/login');
    await this.page.fill('[data-testid="email"]', email);
    await this.page.fill('[data-testid="password"]', password);
    await this.page.click('[data-testid="login-button"]');
    await this.page.waitForURL('/dashboard');
  }

  async waitForNewBookingNotification() {
    await this.page.waitForSelector('[data-testid="booking-notification"]', { timeout: 30000 });
  }

  async getBookingCount() {
    const count = await this.page.locator('[data-testid="booking-count"]').textContent();
    return parseInt(count || '0');
  }

  async navigateToBookings() {
    await this.page.click('[data-testid="nav-bookings"]');
    await this.page.waitForURL('/dashboard/bookings');
  }

  async getLatestBooking() {
    const latestBooking = this.page.locator('[data-testid="booking-item"]').first();
    return {
      reference: await latestBooking.locator('[data-testid="booking-reference"]').textContent(),
      clientName: await latestBooking.locator('[data-testid="client-name"]').textContent(),
      eventType: await latestBooking.locator('[data-testid="event-type"]').textContent(),
      status: await latestBooking.locator('[data-testid="booking-status"]').textContent(),
    };
  }
}

// Performance measurement helpers
export const measurePageLoad = async (page: Page, url: string) => {
  const startTime = Date.now();
  await page.goto(url);
  await page.waitForLoadState('networkidle');
  const endTime = Date.now();
  return endTime - startTime;
};

export const measureFormSubmission = async (page: Page, submitAction: () => Promise<void>) => {
  const startTime = Date.now();
  await submitAction();
  await page.waitForLoadState('networkidle');
  const endTime = Date.now();
  return endTime - startTime;
};

// Network interception helpers
export const interceptEmailJS = async (page: Page) => {
  let emailSent = false;
  let emailData: any = null;

  await page.route('**/api.emailjs.com/**', (route) => {
    emailSent = true;
    emailData = route.request().postDataJSON();
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({ status: 'OK' })
    });
  });

  return {
    wasEmailSent: () => emailSent,
    getEmailData: () => emailData
  };
};

export const interceptSupabase = async (page: Page) => {
  const requests: any[] = [];

  await page.route('**/supabase.co/**', (route) => {
    requests.push({
      url: route.request().url(),
      method: route.request().method(),
      data: route.request().postDataJSON()
    });
    route.continue();
  });

  return {
    getRequests: () => requests,
    getLastRequest: () => requests[requests.length - 1]
  };
};

// Accessibility helpers
export const checkAccessibility = async (page: Page) => {
  try {
    const { injectAxe, checkA11y } = await import('axe-playwright');

    await injectAxe(page);
    const results = await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true }
    });

    return results;
  } catch (error) {
    console.warn('Accessibility check failed:', error);
    return null;
  }
};

// Mobile responsiveness helpers
export const checkMobileResponsiveness = async (page: Page, breakpoints: number[]) => {
  const results: any[] = [];

  for (const width of breakpoints) {
    await page.setViewportSize({ width, height: 800 });
    await page.waitForTimeout(1000); // Allow layout to settle

    // Check if mobile menu is visible
    const mobileMenuVisible = await page.locator('[data-testid="mobile-menu"]').isVisible().catch(() => false);

    // Check if form is properly sized
    const formWidth = await page.locator('[data-testid="booking-form"]').boundingBox().catch(() => null);

    results.push({
      breakpoint: width,
      mobileMenuVisible,
      formFitsViewport: formWidth ? formWidth.width <= width : false,
      screenshot: await page.screenshot({ fullPage: true })
    });
  }

  return results;
};

// Database helpers
export const cleanupTestData = async () => {
  // This would connect to test database and clean up test bookings
  // Implementation depends on test database setup
  console.log('Cleaning up test data...');
  return Promise.resolve();
};

export const createTestPhotographer = async () => {
  // This would create a test photographer account
  // Implementation depends on test database setup
  return Promise.resolve({
    email: '<EMAIL>',
    password: 'TestPassword123!',
    profile: {
      business_name: 'Test Photography Studio',
      owner_name: 'Test Photographer'
    }
  });
};
