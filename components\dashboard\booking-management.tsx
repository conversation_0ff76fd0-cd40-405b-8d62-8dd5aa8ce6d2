'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Calendar, 
  Filter, 
  Search, 
  Plus,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  Phone,
  Mail,
  MapPin
} from 'lucide-react';
import { useBookings, useUpdateBooking, useDeleteBooking } from '@/lib/hooks/use-bookings';
import { useDashboardStore } from '@/lib/store/dashboard-store';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { cn } from '@/lib/utils';

const statusColors = {
  pending: 'bg-amber-100 text-amber-800 border-amber-200',
  confirmed: 'bg-green-100 text-green-800 border-green-200',
  rejected: 'bg-red-100 text-red-800 border-red-200',
  completed: 'bg-blue-100 text-blue-800 border-blue-200',
  cancelled: 'bg-gray-100 text-gray-800 border-gray-200',
};

const statusIcons = {
  pending: Clock,
  confirmed: CheckCircle,
  rejected: XCircle,
  completed: CheckCircle,
  cancelled: XCircle,
};

export function BookingManagement() {
  const { data: bookings, isLoading } = useBookings();
  const updateBooking = useUpdateBooking();
  const deleteBooking = useDeleteBooking();
  const { ui, actions } = useDashboardStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedBooking, setSelectedBooking] = useState<any>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Filter bookings based on search and status
  const filteredBookings = bookings?.filter(booking => {
    const matchesSearch = 
      booking.client_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      booking.event_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      booking.booking_reference.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  }) || [];

  const handleStatusUpdate = async (bookingId: string, newStatus: string) => {
    try {
      await updateBooking.mutateAsync({
        id: bookingId,
        updates: { status: newStatus }
      });
    } catch (error) {
      console.error('Failed to update booking status:', error);
    }
  };

  const handleDeleteBooking = async (bookingId: string) => {
    if (confirm('Are you sure you want to delete this booking?')) {
      try {
        await deleteBooking.mutateAsync(bookingId);
      } catch (error) {
        console.error('Failed to delete booking:', error);
      }
    }
  };

  const openBookingDetails = (booking: any) => {
    setSelectedBooking(booking);
    setShowDetails(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center space-y-4">
          <LoadingSpinner size="lg" />
          <p className="text-gray-600">Loading bookings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Booking Management</h1>
          <p className="text-gray-600">Manage and track all your photography bookings</p>
        </div>
        <Button className="bg-gradient-to-r from-rose-500 to-amber-500 hover:from-rose-600 hover:to-amber-600">
          <Plus className="w-4 h-4 mr-2" />
          New Booking
        </Button>
      </div>

      {/* Filters */}
      <Card className="glass border-white/20">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search bookings..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 glass border-white/20"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48 glass border-white/20">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bookings List */}
      <div className="space-y-4">
        <AnimatePresence mode="popLayout">
          {filteredBookings.length > 0 ? (
            filteredBookings.map((booking, index) => {
              const StatusIcon = statusIcons[booking.status as keyof typeof statusIcons];
              
              return (
                <motion.div
                  key={booking.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.05 }}
                  layout
                >
                  <Card className="glass border-white/20 hover:border-white/30 transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-rose-500 to-amber-500 rounded-lg flex items-center justify-center">
                            <Calendar className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{booking.client_name}</h3>
                            <p className="text-sm text-gray-600">{booking.event_type}</p>
                            <p className="text-xs text-gray-500">Ref: {booking.booking_reference}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4">
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900">
                              {booking.event_date && new Date(booking.event_date).toLocaleDateString()}
                            </p>
                            <p className="text-xs text-gray-500">
                              {new Date(booking.created_at).toLocaleDateString()}
                            </p>
                          </div>
                          
                          <Badge className={cn('border', statusColors[booking.status as keyof typeof statusColors])}>
                            <StatusIcon className="w-3 h-3 mr-1" />
                            {booking.status}
                          </Badge>
                          
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => openBookingDetails(booking)}
                              className="h-8 w-8"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            
                            {booking.status === 'pending' && (
                              <>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleStatusUpdate(booking.id, 'confirmed')}
                                  className="h-8 w-8 text-green-600 hover:text-green-700"
                                >
                                  <CheckCircle className="w-4 h-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleStatusUpdate(booking.id, 'rejected')}
                                  className="h-8 w-8 text-red-600 hover:text-red-700"
                                >
                                  <XCircle className="w-4 h-4" />
                                </Button>
                              </>
                            )}
                            
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteBooking(booking.id)}
                              className="h-8 w-8 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })
          ) : (
            <Card className="glass border-white/20">
              <CardContent className="p-12 text-center">
                <Calendar className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No bookings found</h3>
                <p className="text-gray-600 mb-6">
                  {searchQuery || statusFilter !== 'all' 
                    ? 'Try adjusting your search or filters'
                    : 'Your bookings will appear here when clients submit inquiries'
                  }
                </p>
                <Button className="bg-gradient-to-r from-rose-500 to-amber-500">
                  <Plus className="w-4 h-4 mr-2" />
                  Create First Booking
                </Button>
              </CardContent>
            </Card>
          )}
        </AnimatePresence>
      </div>

      {/* Booking Details Dialog */}
      <BookingDetailsDialog 
        booking={selectedBooking}
        open={showDetails}
        onOpenChange={setShowDetails}
        onStatusUpdate={handleStatusUpdate}
      />
    </div>
  );
}

// Booking Details Dialog Component
function BookingDetailsDialog({ 
  booking, 
  open, 
  onOpenChange, 
  onStatusUpdate 
}: {
  booking: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStatusUpdate: (id: string, status: string) => void;
}) {
  if (!booking) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl glass border-white/20">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Booking Details - {booking.booking_reference}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Client Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900">Client Information</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Name:</span>
                  <span>{booking.client_name}</span>
                </div>
                {booking.client_email && (
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    <span>{booking.client_email}</span>
                  </div>
                )}
                {booking.client_phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    <span>{booking.client_phone}</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900">Event Details</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Type:</span>
                  <span className="capitalize">{booking.event_type}</span>
                </div>
                {booking.event_date && (
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(booking.event_date).toLocaleDateString()}</span>
                  </div>
                )}
                {booking.venue && (
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    <span>{booking.venue}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Status Actions */}
          <div className="flex gap-2 pt-4 border-t border-white/10">
            <Button
              onClick={() => onStatusUpdate(booking.id, 'confirmed')}
              className="bg-green-600 hover:bg-green-700"
              disabled={booking.status === 'confirmed'}
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              Confirm
            </Button>
            <Button
              onClick={() => onStatusUpdate(booking.id, 'rejected')}
              variant="destructive"
              disabled={booking.status === 'rejected'}
            >
              <XCircle className="w-4 h-4 mr-2" />
              Reject
            </Button>
            <Button
              onClick={() => onStatusUpdate(booking.id, 'completed')}
              variant="outline"
              disabled={booking.status === 'completed'}
            >
              Mark Complete
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
