import { test, expect } from '@playwright/test';
import { BookingFormPage, generateBookingData, interceptSupabase } from '../utils/test-helpers';

/**
 * Database Integration Tests
 * Tests Supabase database operations, booking creation, and client auto-creation
 */

test.describe('Database Integration - Booking Operations', () => {
  test('should create booking record in database @desktop', async ({ page }) => {
    const bookingData = generateBookingData('engagement');
    const supabaseInterceptor = await interceptSupabase(page);
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Submit booking form', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
      await page.selectOption('[data-testid="outfit-changes"]', bookingData.outfitChanges);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify database booking creation', async () => {
      const requests = supabaseInterceptor.getRequests();
      const bookingInsert = requests.find(r => 
        r.url.includes('/bookings') && 
        r.method === 'POST'
      );

      expect(bookingInsert).toBeDefined();
      expect(bookingInsert.data).toMatchObject({
        client_name: bookingData.brideAName,
        client_email: bookingData.email,
        client_phone: bookingData.phoneNumber,
        event_type: 'engagement',
        package_type: bookingData.package,
        status: 'pending',
        email_sent: false,
        whatsapp_sent: false
      });

      // Verify form_data contains complete form information
      expect(bookingInsert.data.form_data).toMatchObject({
        brideAName: bookingData.brideAName,
        groomName: bookingData.groomName,
        email: bookingData.email,
        phoneNumber: bookingData.phoneNumber,
        eventType: 'engagement',
        package: bookingData.package
      });
    });

    await test.step('Verify booking reference generation', async () => {
      const requests = supabaseInterceptor.getRequests();
      const referenceCall = requests.find(r => 
        r.url.includes('generate_booking_reference')
      );

      expect(referenceCall).toBeDefined();
      
      // Booking reference should be displayed on success page
      const bookingRef = await page.locator('[data-testid="booking-reference"]').textContent();
      expect(bookingRef).toMatch(/^BKG-\d{8}-\d{3}$/); // Format: BKG-YYYYMMDD-XXX
    });
  });

  test('should auto-create client from booking @desktop', async ({ page }) => {
    const bookingData = generateBookingData('wedding');
    const supabaseInterceptor = await interceptSupabase(page);
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Submit booking with new client', async () => {
      await bookingPage.fillStep1('wedding');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="registration-time"]', bookingData.registrationTime);
      await page.fill('[data-testid="ceremony-time"]', bookingData.ceremonyTime);
      await page.fill('[data-testid="event-end-time"]', bookingData.eventEndTime);
      await page.fill('[data-testid="guest-count"]', bookingData.guestCount);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify client auto-creation', async () => {
      const requests = supabaseInterceptor.getRequests();
      
      // Should trigger client creation via database trigger
      const bookingInsert = requests.find(r => 
        r.url.includes('/bookings') && 
        r.method === 'POST'
      );
      
      expect(bookingInsert).toBeDefined();
      
      // The trigger should auto-create client and link to booking
      // This would be verified by checking the client_id is populated
      expect(bookingInsert.data.client_name).toBe(bookingData.brideAName);
      expect(bookingInsert.data.client_email).toBe(bookingData.email);
      expect(bookingInsert.data.client_phone).toBe(bookingData.phoneNumber);
    });
  });

  test('should handle existing client booking @desktop', async ({ page }) => {
    const bookingData = generateBookingData('homecoming');
    // Use same email as previous test to simulate existing client
    bookingData.email = '<EMAIL>';
    
    const supabaseInterceptor = await interceptSupabase(page);
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Submit booking with existing client email', async () => {
      await bookingPage.fillStep1('homecoming');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="homecoming-date"]', bookingData.homecomingDate);
      await page.fill('[data-testid="homecoming-venue"]', bookingData.homecomingVenue);
      await page.fill('[data-testid="homecoming-end-time"]', bookingData.homecomingEndTime);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify existing client handling', async () => {
      const requests = supabaseInterceptor.getRequests();
      const bookingInsert = requests.find(r => 
        r.url.includes('/bookings') && 
        r.method === 'POST'
      );

      expect(bookingInsert).toBeDefined();
      
      // Should link to existing client instead of creating new one
      expect(bookingInsert.data.client_email).toBe(bookingData.email);
      
      // The database trigger should handle linking to existing client
      // and updating their booking count
    });
  });

  test('should log analytics events @desktop', async ({ page }) => {
    const bookingData = generateBookingData('triple-combo');
    const supabaseInterceptor = await interceptSupabase(page);
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Submit triple combo booking', async () => {
      await bookingPage.fillStep1('triple-combo');
      await bookingPage.fillStep2(bookingData.package);
      
      // Fill all event details
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
      await page.fill('[data-testid="wedding-date"]', bookingData.weddingDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="homecoming-date"]', bookingData.homecomingDate);
      await page.fill('[data-testid="homecoming-venue"]', bookingData.homecomingVenue);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify analytics event logging', async () => {
      const requests = supabaseInterceptor.getRequests();
      const analyticsCall = requests.find(r => 
        r.url.includes('log_analytics_event')
      );

      expect(analyticsCall).toBeDefined();
      expect(analyticsCall.data).toMatchObject({
        p_event_type: 'booking_submitted',
        p_event_data: expect.objectContaining({
          event_type: 'triple-combo',
          package: bookingData.package,
          source: 'booking_form'
        })
      });
    });
  });
});

test.describe('Database Integration - Error Handling', () => {
  test('should handle database connection failure gracefully @desktop', async ({ page }) => {
    const bookingData = generateBookingData('engagement');
    
    // Simulate database connection failure
    await page.route('**/supabase.co/**', (route) => {
      route.abort('failed');
    });
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Submit booking with database offline', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="desired-location"]', bookingData.desiredLocation);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
    });

    await test.step('Verify fallback behavior', async () => {
      // Should still show success message (fallback mode)
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
      
      // Should show fallback booking reference
      const bookingRef = await page.locator('[data-testid="booking-reference"]').textContent();
      expect(bookingRef).toMatch(/^LOCAL-\d+$/); // Local fallback format
      
      // Should still send email notification
      await expect(page.locator('[data-testid="email-sent-confirmation"]')).toBeVisible();
    });
  });

  test('should handle duplicate booking reference @desktop', async ({ page }) => {
    const bookingData = generateBookingData('wedding');
    
    // Mock duplicate reference scenario
    let callCount = 0;
    await page.route('**/generate_booking_reference', (route) => {
      callCount++;
      if (callCount === 1) {
        // First call returns duplicate reference
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify('BKG-20241219-001')
        });
      } else {
        // Second call returns unique reference
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify('BKG-20241219-002')
        });
      }
    });
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Submit booking that triggers duplicate reference', async () => {
      await bookingPage.fillStep1('wedding');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify unique reference generation', async () => {
      // Should eventually get unique reference
      const bookingRef = await page.locator('[data-testid="booking-reference"]').textContent();
      expect(bookingRef).toMatch(/^BKG-\d{8}-\d{3}$/);
      
      // Should have called reference generation multiple times
      expect(callCount).toBeGreaterThan(1);
    });
  });

  test('should validate database schema constraints @desktop', async ({ page }) => {
    const bookingData = generateBookingData('engagement');
    
    // Mock database constraint violation
    await page.route('**/bookings', (route) => {
      if (route.request().method() === 'POST') {
        route.fulfill({
          status: 409,
          contentType: 'application/json',
          body: JSON.stringify({
            code: '23505',
            message: 'duplicate key value violates unique constraint'
          })
        });
      } else {
        route.continue();
      }
    });
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Submit booking that violates constraints', async () => {
      await bookingPage.fillStep1('engagement');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="event-date"]', bookingData.eventDate);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
    });

    await test.step('Verify error handling', async () => {
      // Should show appropriate error message
      await expect(page.locator('[data-testid="error-message"]')).toContainText('booking reference');
      
      // Should offer retry option
      await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
    });
  });
});

test.describe('Database Integration - Data Integrity', () => {
  test('should maintain data consistency across tables @desktop', async ({ page }) => {
    const bookingData = generateBookingData('wedding-homecoming');
    const supabaseInterceptor = await interceptSupabase(page);
    
    const bookingPage = new BookingFormPage(page);
    await bookingPage.goto();

    await test.step('Submit dual combo booking', async () => {
      await bookingPage.fillStep1('wedding-homecoming');
      await bookingPage.fillStep2(bookingData.package);
      
      await page.fill('[data-testid="wedding-date"]', bookingData.weddingDate);
      await page.fill('[data-testid="venue"]', bookingData.venue);
      await page.fill('[data-testid="homecoming-date"]', bookingData.homecomingDate);
      await page.fill('[data-testid="homecoming-venue"]', bookingData.homecomingVenue);
      await page.click('[data-testid="next-step"]');
      
      await bookingPage.fillStep4(bookingData);
      await bookingPage.submitForm();
      await bookingPage.waitForSuccessMessage();
    });

    await test.step('Verify data consistency', async () => {
      const requests = supabaseInterceptor.getRequests();
      
      // Booking should be created
      const bookingInsert = requests.find(r => 
        r.url.includes('/bookings') && r.method === 'POST'
      );
      expect(bookingInsert).toBeDefined();
      
      // Client should be auto-created/linked
      expect(bookingInsert.data.client_name).toBe(bookingData.brideAName);
      expect(bookingInsert.data.client_email).toBe(bookingData.email);
      
      // Analytics event should be logged
      const analyticsCall = requests.find(r => 
        r.url.includes('log_analytics_event')
      );
      expect(analyticsCall).toBeDefined();
      
      // All data should reference the same booking
      expect(analyticsCall.data.p_event_data.event_type).toBe('wedding-homecoming');
    });
  });

  test('should handle concurrent booking submissions @desktop', async ({ page, context }) => {
    const bookingData1 = generateBookingData('engagement');
    const bookingData2 = generateBookingData('wedding');
    
    // Create two pages for concurrent submissions
    const page2 = await context.newPage();
    
    const bookingPage1 = new BookingFormPage(page);
    const bookingPage2 = new BookingFormPage(page2);
    
    await Promise.all([
      bookingPage1.goto(),
      bookingPage2.goto()
    ]);

    await test.step('Submit concurrent bookings', async () => {
      // Start both booking flows simultaneously
      const booking1Promise = (async () => {
        await bookingPage1.fillStep1('engagement');
        await bookingPage1.fillStep2(bookingData1.package);
        await page.fill('[data-testid="event-date"]', bookingData1.eventDate);
        await page.click('[data-testid="next-step"]');
        await bookingPage1.fillStep4(bookingData1);
        await bookingPage1.submitForm();
        await bookingPage1.waitForSuccessMessage();
      })();
      
      const booking2Promise = (async () => {
        await bookingPage2.fillStep1('wedding');
        await bookingPage2.fillStep2(bookingData2.package);
        await page2.fill('[data-testid="event-date"]', bookingData2.eventDate);
        await page2.fill('[data-testid="venue"]', bookingData2.venue);
        await page2.click('[data-testid="next-step"]');
        await bookingPage2.fillStep4(bookingData2);
        await bookingPage2.submitForm();
        await bookingPage2.waitForSuccessMessage();
      })();
      
      // Wait for both to complete
      await Promise.all([booking1Promise, booking2Promise]);
    });

    await test.step('Verify both bookings succeeded', async () => {
      // Both should have unique booking references
      const ref1 = await page.locator('[data-testid="booking-reference"]').textContent();
      const ref2 = await page2.locator('[data-testid="booking-reference"]').textContent();
      
      expect(ref1).toMatch(/^BKG-\d{8}-\d{3}$/);
      expect(ref2).toMatch(/^BKG-\d{8}-\d{3}$/);
      expect(ref1).not.toBe(ref2); // Should be unique
    });
    
    await page2.close();
  });
});
