'use client';

import { useEffect, useRef } from 'react';
import { RealtimeChannel } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase/client';
import { useDashboardStore } from '@/lib/store/dashboard-store';
import { useQueryClient } from '@tanstack/react-query';
import { bookingKeys } from './use-bookings';
import { Database } from '@/lib/supabase/types';

type Booking = Database['public']['Tables']['bookings']['Row'];
type Client = Database['public']['Tables']['clients']['Row'];

export function useRealtimeBookings() {
  const channelRef = useRef<RealtimeChannel | null>(null);
  const { actions } = useDashboardStore();
  const queryClient = useQueryClient();

  useEffect(() => {
    // Create realtime channel for bookings
    const channel = supabase
      .channel('bookings-realtime')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'bookings',
        },
        (payload) => {
          console.log('New booking received via realtime:', payload.new);
          
          const newBooking = payload.new as Booking;
          
          // Add to store
          actions.addBooking(newBooking);
          
          // Invalidate and refetch queries
          queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
          
          // Show notification with sound
          actions.addNotification({
            type: 'info',
            title: 'New Booking Received! 🎉',
            message: `${newBooking.client_name} just submitted a ${newBooking.event_type} booking inquiry.`,
          });

          // Play notification sound
          playNotificationSound();
          
          // Log analytics
          supabase.rpc('log_analytics_event', {
            p_event_type: 'booking_received_realtime',
            p_event_data: { 
              event_type: newBooking.event_type,
              client_name: newBooking.client_name,
              source: 'realtime'
            },
            p_booking_id: newBooking.id,
          });
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'bookings',
        },
        (payload) => {
          console.log('Booking updated via realtime:', payload.new);
          
          const updatedBooking = payload.new as Booking;
          
          // Update store
          actions.updateBooking(updatedBooking.id, updatedBooking);
          
          // Invalidate queries
          queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
          queryClient.invalidateQueries({ queryKey: bookingKeys.detail(updatedBooking.id) });
          
          // Show notification for status changes
          if (payload.old && (payload.old as Booking).status !== updatedBooking.status) {
            actions.addNotification({
              type: 'success',
              title: 'Booking Status Updated',
              message: `${updatedBooking.client_name}'s booking is now ${updatedBooking.status}.`,
            });
          }
        }
      )
      .subscribe((status) => {
        console.log('Realtime subscription status:', status);
        
        if (status === 'SUBSCRIBED') {
          actions.addNotification({
            type: 'success',
            title: 'Real-time Connected',
            message: 'You will receive instant notifications for new bookings.',
          });
        } else if (status === 'CHANNEL_ERROR') {
          actions.addNotification({
            type: 'error',
            title: 'Real-time Connection Error',
            message: 'Failed to connect to real-time updates. Please refresh the page.',
          });
        }
      });

    channelRef.current = channel;

    // Cleanup on unmount
    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
      }
    };
  }, [actions, queryClient]);

  return {
    isConnected: channelRef.current?.state === 'joined',
    disconnect: () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
      }
    },
  };
}

export function useRealtimeClients() {
  const channelRef = useRef<RealtimeChannel | null>(null);
  const { actions } = useDashboardStore();
  const queryClient = useQueryClient();

  useEffect(() => {
    const channel = supabase
      .channel('clients-realtime')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'clients',
        },
        (payload) => {
          console.log('New client created via realtime:', payload.new);
          
          const newClient = payload.new as Client;
          
          // Add to store
          actions.addClient(newClient);
          
          // Invalidate queries
          queryClient.invalidateQueries({ queryKey: ['clients'] });
          
          // Show notification
          actions.addNotification({
            type: 'info',
            title: 'New Client Added',
            message: `${newClient.name} has been added to your client database.`,
          });
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'clients',
        },
        (payload) => {
          console.log('Client updated via realtime:', payload.new);
          
          const updatedClient = payload.new as Client;
          
          // Update store
          actions.updateClient(updatedClient.id, updatedClient);
          
          // Invalidate queries
          queryClient.invalidateQueries({ queryKey: ['clients'] });
        }
      )
      .subscribe();

    channelRef.current = channel;

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
      }
    };
  }, [actions, queryClient]);

  return {
    isConnected: channelRef.current?.state === 'joined',
  };
}

// Notification sound utility
function playNotificationSound() {
  try {
    // Create a simple notification sound using Web Audio API
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    
    // Create a simple beep sound
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
  } catch (error) {
    console.log('Could not play notification sound:', error);
  }
}

// Hook to manage all realtime subscriptions
export function useRealtimeSubscriptions() {
  const bookingsRealtime = useRealtimeBookings();
  const clientsRealtime = useRealtimeClients();

  return {
    bookings: bookingsRealtime,
    clients: clientsRealtime,
    isConnected: bookingsRealtime.isConnected && clientsRealtime.isConnected,
    disconnectAll: () => {
      bookingsRealtime.disconnect();
    },
  };
}
