import { FullConfig } from '@playwright/test';
import { cleanupTestData } from './utils/test-helpers';
import fs from 'fs';
import path from 'path';

/**
 * Global Teardown for Tera Works Photographer Booking System Tests
 * Runs once after all tests to clean up the test environment
 */
async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown...');

  try {
    // Clean up test data from database
    await cleanupTestData();
    console.log('🗑️ Test data cleaned up');

    // Remove authentication state files
    const authDir = path.join(__dirname, 'auth');
    if (fs.existsSync(authDir)) {
      fs.rmSync(authDir, { recursive: true, force: true });
      console.log('🔐 Authentication state files removed');
    }

    // Generate test summary report
    await generateTestSummary();

    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Error during global teardown:', error);
    // Don't throw error to avoid masking test failures
  }
}

async function generateTestSummary() {
  try {
    const resultsPath = path.join(__dirname, '..', 'test-results', 'results.json');
    
    if (!fs.existsSync(resultsPath)) {
      console.log('📊 No test results found for summary generation');
      return;
    }

    const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
    
    const summary = {
      timestamp: new Date().toISOString(),
      totalTests: results.stats?.total || 0,
      passed: results.stats?.passed || 0,
      failed: results.stats?.failed || 0,
      skipped: results.stats?.skipped || 0,
      duration: results.stats?.duration || 0,
      suites: results.suites?.map((suite: any) => ({
        title: suite.title,
        tests: suite.tests?.length || 0,
        passed: suite.tests?.filter((t: any) => t.status === 'passed').length || 0,
        failed: suite.tests?.filter((t: any) => t.status === 'failed').length || 0,
      })) || []
    };

    const summaryPath = path.join(__dirname, '..', 'test-results', 'summary.json');
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
    
    console.log('📊 Test summary generated:', {
      total: summary.totalTests,
      passed: summary.passed,
      failed: summary.failed,
      duration: `${Math.round(summary.duration / 1000)}s`
    });

  } catch (error) {
    console.error('❌ Failed to generate test summary:', error);
  }
}

export default globalTeardown;
