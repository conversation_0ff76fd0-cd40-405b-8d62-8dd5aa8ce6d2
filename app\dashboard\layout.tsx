import { AuthProvider } from '@/lib/auth/auth-context';
import { AuthGuard } from '@/lib/auth/auth-guard';
import { DashboardShell } from '@/components/dashboard/dashboard-shell';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthProvider>
      <AuthGuard requireAuth={true}>
        <DashboardShell>
          {children}
        </DashboardShell>
      </AuthGuard>
    </AuthProvider>
  );
}
