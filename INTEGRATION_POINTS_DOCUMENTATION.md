# Integration Points Documentation
## Tera Works Photographer Booking System

---

## 1. EmailJS Integration

### Current Configuration ✅ **ACTIVE**

**Service Details:**
- Service ID: `service_ywznx8m`
- Template ID: `template_rae6acc`
- Public Key: `pyjj4z90GoTsyHDQT`
- Target Email: `<EMAIL>`

**Implementation:**
```javascript
// EmailJS initialization and sending
import emailjs from '@emailjs/browser';

// Initialize EmailJS
emailjs.init('pyjj4z90GoTsyHDQT');

// Send booking notification
const sendBookingEmail = async (formData) => {
  const emailData = {
    client_name: formData.brideAName,
    client_email: formData.email,
    client_phone: formData.phoneNumber,
    event_type: formData.eventType,
    event_date: formData.eventDate,
    venue_location: formData.venue,
    booking_reference: formData.bookingReference,
    package_name: formData.package,
    special_requests: formData.additionalNotes,
    
    // Boolean flags for conditional template content
    is_engagement: formData.eventType === 'engagement',
    is_wedding: formData.eventType === 'wedding',
    is_homecoming: formData.eventType === 'homecoming',
    is_combo: formData.eventType.includes('-'),
    
    // Calendar integration
    calendar_link: generateCalendarLink(formData),
    
    // Metadata
    submission_time: new Date().toLocaleString(),
    whatsapp_link: generateWhatsAppURL(formData)
  };

  return await emailjs.send(
    'service_ywznx8m',
    'template_rae6acc',
    emailData,
    { publicKey: 'pyjj4z90GoTsyHDQT' }
  );
};
```

**Email Template Variables:**
```html
<!-- EmailJS Template Structure -->
<h2>New Booking Inquiry - {{booking_reference}}</h2>

<h3>Client Information</h3>
<p><strong>Name:</strong> {{client_name}}</p>
<p><strong>Email:</strong> {{client_email}}</p>
<p><strong>Phone:</strong> {{client_phone}}</p>

<h3>Event Details</h3>
<p><strong>Type:</strong> {{event_type}}</p>
<p><strong>Date:</strong> {{event_date}}</p>
<p><strong>Venue:</strong> {{venue_location}}</p>
<p><strong>Package:</strong> {{package_name}}</p>

{{#is_engagement}}
<h4>Engagement Session Details</h4>
<p>Special engagement photography session</p>
{{/is_engagement}}

{{#is_wedding}}
<h4>Wedding Photography Details</h4>
<p>Full wedding day coverage</p>
{{/is_wedding}}

<h3>Calendar Integration</h3>
<a href="{{calendar_link}}" target="_blank">Add to Google Calendar</a>

<h3>Quick Actions</h3>
<a href="{{whatsapp_link}}" target="_blank">Reply via WhatsApp</a>

<p><strong>Special Requests:</strong> {{special_requests}}</p>
<p><strong>Submitted:</strong> {{submission_time}}</p>
```

---

## 2. WhatsApp Integration

### Current Configuration ✅ **ACTIVE**

**Service Details:**
- Business Number: `+***********`
- Integration Type: URL-based (WhatsApp Web)
- Message Format: Pre-filled booking details

**Implementation:**
```javascript
// WhatsApp URL generation
const generateWhatsAppURL = (formData, bookingReference) => {
  const phoneNumber = '***********'; // Remove + for URL
  
  const message = `
🎉 New Booking Inquiry - ${bookingReference}

📋 Event Details:
• Type: ${formData.eventType}
• Date: ${formData.eventDate}
• Venue: ${formData.venue}
• Package: ${formData.package}

👤 Client Information:
• Name: ${formData.brideAName}
• Email: ${formData.email}
• Phone: ${formData.phoneNumber}

💬 Special Requests:
${formData.additionalNotes || 'None'}

Please confirm availability for this booking.
  `.trim();
  
  return `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
};

// Usage in booking form
const handleWhatsAppRedirect = () => {
  const whatsappURL = generateWhatsAppURL(formData, bookingReference);
  window.open(whatsappURL, '_blank');
};
```

**Message Templates by Event Type:**
```javascript
const getWhatsAppTemplate = (eventType, formData) => {
  const templates = {
    engagement: `
🎉 Engagement Session Inquiry - ${formData.bookingReference}

📸 Session Details:
• Date: ${formData.eventDate}
• Location: ${formData.desiredLocation}
• Package: ${formData.package}
• Outfit Changes: ${formData.outfitChanges}

👫 Couple Information:
• Names: ${formData.brideAName} & ${formData.groomName}
• Contact: ${formData.email} | ${formData.phoneNumber}

Ready to capture your love story! 💕
    `,
    
    wedding: `
💒 Wedding Photography Inquiry - ${formData.bookingReference}

🎊 Wedding Details:
• Date: ${formData.eventDate}
• Venue: ${formData.venue}
• Package: ${formData.package}
• Guest Count: ${formData.guestCount}

⏰ Timeline:
• Registration: ${formData.registrationTime}
• Ceremony: ${formData.ceremonyTime}
• End Time: ${formData.eventEndTime}

👰🤵 Couple: ${formData.brideAName} & ${formData.groomName}

Let's make your special day unforgettable! ✨
    `,
    
    homecoming: `
🏠 Homecoming Celebration Inquiry - ${formData.bookingReference}

🎉 Event Details:
• Date: ${formData.homecomingDate}
• Venue: ${formData.homecomingVenue}
• Package: ${formData.package}

👫 Couple: ${formData.brideAName} & ${formData.groomName}
• Contact: ${formData.email} | ${formData.phoneNumber}

Ready to capture your homecoming celebration! 🎊
    `
  };
  
  return templates[eventType] || templates.engagement;
};
```

---

## 3. Google Calendar Integration

### Current Configuration 🔄 **PLANNED**

**Service Details:**
- API: Google Calendar API v3
- Authentication: OAuth 2.0
- Scope: `https://www.googleapis.com/auth/calendar`

**Implementation Plan:**
```javascript
// Google Calendar API integration
const addToGoogleCalendar = async (eventData) => {
  const event = {
    summary: `${eventData.eventType} - ${eventData.clientName}`,
    description: `
Photography Session Details:
- Client: ${eventData.clientName}
- Email: ${eventData.clientEmail}
- Phone: ${eventData.clientPhone}
- Package: ${eventData.packageType}
- Special Requests: ${eventData.notes}
- Booking Reference: ${eventData.bookingReference}
    `,
    start: {
      dateTime: `${eventData.eventDate}T${eventData.startTime}:00`,
      timeZone: 'Asia/Colombo'
    },
    end: {
      dateTime: `${eventData.eventDate}T${eventData.endTime}:00`,
      timeZone: 'Asia/Colombo'
    },
    location: eventData.venue,
    attendees: [
      { email: eventData.clientEmail },
      { email: '<EMAIL>' }
    ],
    reminders: {
      useDefault: false,
      overrides: [
        { method: 'email', minutes: 24 * 60 }, // 1 day before
        { method: 'popup', minutes: 60 }       // 1 hour before
      ]
    }
  };

  return await gapi.client.calendar.events.insert({
    calendarId: 'primary',
    resource: event
  });
};
```

**Calendar Link Generation (Current):**
```javascript
// Generate Google Calendar links for clients
const generateCalendarLink = (title, date, startTime, endTime, location, description) => {
  const startDateTime = `${date}T${startTime}:00`;
  const endDateTime = `${date}T${endTime}:00`;
  
  const params = new URLSearchParams({
    action: 'TEMPLATE',
    text: title,
    dates: `${startDateTime.replace(/[-:]/g, '')}/${endDateTime.replace(/[-:]/g, '')}`,
    location: location,
    details: description,
    ctz: 'Asia/Colombo'
  });
  
  return `https://calendar.google.com/calendar/render?${params.toString()}`;
};

// Usage for different event types
const generateEventCalendarLinks = (formData) => {
  const links = {};
  
  if (formData.eventType === 'engagement') {
    links.engagement = generateCalendarLink(
      `${formData.brideAName} & ${formData.groomName} - Engagement Session`,
      formData.eventDate,
      '10:00',
      '12:00',
      formData.desiredLocation,
      'Pre-wedding engagement photography session'
    );
  }
  
  if (formData.eventType === 'wedding' || formData.eventType.includes('wedding')) {
    links.wedding = generateCalendarLink(
      `${formData.brideAName} & ${formData.groomName} - Wedding`,
      formData.eventDate,
      formData.registrationTime,
      formData.eventEndTime,
      formData.venue,
      'Wedding photography coverage'
    );
  }
  
  if (formData.eventType === 'homecoming' || formData.eventType.includes('homecoming')) {
    links.homecoming = generateCalendarLink(
      `${formData.brideAName} & ${formData.groomName} - Homecoming`,
      formData.homecomingDate,
      '10:00',
      formData.homecomingEndTime,
      formData.homecomingVenue,
      'Homecoming celebration photography'
    );
  }
  
  return links;
};
```

---

## 4. Payment Gateway Integration

### Planned Configuration 🔄 **FUTURE**

**Primary: PayHere (Sri Lankan)**
```javascript
// PayHere integration for local payments
const initiatePayHerePayment = async (bookingData) => {
  const payment = {
    sandbox: process.env.NODE_ENV !== 'production',
    merchant_id: process.env.PAYHERE_MERCHANT_ID,
    return_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/success`,
    cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/cancel`,
    notify_url: `${process.env.NEXT_PUBLIC_APP_URL}/api/payment/notify`,
    
    order_id: bookingData.bookingReference,
    items: `${bookingData.eventType} Photography Package`,
    amount: bookingData.packagePrice,
    currency: 'LKR',
    
    first_name: bookingData.clientName.split(' ')[0],
    last_name: bookingData.clientName.split(' ').slice(1).join(' '),
    email: bookingData.clientEmail,
    phone: bookingData.clientPhone,
    address: bookingData.clientAddress || '',
    city: 'Colombo',
    country: 'Sri Lanka'
  };
  
  // Initialize PayHere payment
  payhere.startPayment(payment);
};
```

**Secondary: Stripe (International)**
```javascript
// Stripe integration for international payments
const initiateStripePayment = async (bookingData) => {
  const stripe = await stripePromise;
  
  const { error } = await stripe.redirectToCheckout({
    lineItems: [{
      price_data: {
        currency: 'usd',
        product_data: {
          name: `${bookingData.eventType} Photography Package`,
          description: `Booking Reference: ${bookingData.bookingReference}`
        },
        unit_amount: Math.round(bookingData.packagePrice * 100 / 300), // LKR to USD conversion
      },
      quantity: 1,
    }],
    mode: 'payment',
    successUrl: `${process.env.NEXT_PUBLIC_APP_URL}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
    cancelUrl: `${process.env.NEXT_PUBLIC_APP_URL}/payment/cancel`,
    metadata: {
      booking_reference: bookingData.bookingReference,
      client_email: bookingData.clientEmail
    }
  });
  
  if (error) {
    console.error('Stripe payment error:', error);
  }
};
```

---

## 5. File Storage & CDN Integration

### Supabase Storage Configuration

**Current Setup:**
```javascript
// File upload to Supabase Storage
const uploadFile = async (file, bucket = 'bookings') => {
  const fileExt = file.name.split('.').pop();
  const fileName = `${Date.now()}.${fileExt}`;
  const filePath = `${bucket}/${fileName}`;

  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(filePath, file);

  if (error) {
    throw error;
  }

  // Get public URL
  const { data: { publicUrl } } = supabase.storage
    .from(bucket)
    .getPublicUrl(filePath);

  return { path: filePath, url: publicUrl };
};

// Image optimization and resizing
const uploadOptimizedImage = async (file) => {
  // Client-side image compression
  const compressedFile = await compressImage(file, {
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 0.8
  });

  return await uploadFile(compressedFile, 'portfolio');
};
```

**Storage Buckets:**
- `bookings`: Booking-related documents
- `portfolio`: Photographer portfolio images
- `client-galleries`: Client photo galleries
- `documents`: Contracts and agreements

---

## 6. Analytics & Monitoring Integration

### Google Analytics 4 Configuration

```javascript
// GA4 event tracking
const trackBookingEvent = (eventType, bookingData) => {
  gtag('event', 'booking_submitted', {
    event_category: 'booking',
    event_label: eventType,
    value: bookingData.packagePrice,
    custom_parameters: {
      booking_reference: bookingData.bookingReference,
      event_type: eventType,
      package_type: bookingData.packageType
    }
  });
};

// Custom events for photographer dashboard
const trackDashboardEvent = (action, category = 'dashboard') => {
  gtag('event', action, {
    event_category: category,
    event_label: action
  });
};
```

### Supabase Analytics Integration

```javascript
// Custom analytics events in database
const logAnalyticsEvent = async (eventType, eventData) => {
  await supabase.rpc('log_analytics_event', {
    p_event_type: eventType,
    p_event_data: eventData
  });
};

// Usage examples
await logAnalyticsEvent('booking_submitted', {
  event_type: formData.eventType,
  package: formData.package,
  source: 'booking_form',
  user_agent: navigator.userAgent
});

await logAnalyticsEvent('dashboard_login', {
  photographer_id: user.id,
  login_method: 'email'
});
```

---

## Integration Status Summary

| Service | Status | Configuration | Notes |
|---------|--------|---------------|-------|
| **EmailJS** | ✅ Active | Complete | Fully functional |
| **WhatsApp** | ✅ Active | Complete | URL-based integration |
| **Google Calendar** | 🔄 Planned | Links only | API integration pending |
| **PayHere** | 🔄 Planned | Not configured | For local payments |
| **Stripe** | 🔄 Planned | Not configured | For international payments |
| **Supabase Storage** | ✅ Ready | Schema deployed | File upload ready |
| **Google Analytics** | 🔄 Planned | Not configured | Tracking setup needed |
| **Social Media APIs** | 🔄 Future | Not planned | Portfolio sync potential |

---

## Next Integration Steps

1. **Immediate (Week 1)**
   - Test EmailJS integration
   - Verify WhatsApp URL generation
   - Set up Google Analytics tracking

2. **Short-term (Month 1)**
   - Implement Google Calendar API
   - Configure Supabase Storage buckets
   - Add file upload functionality

3. **Medium-term (Month 2-3)**
   - Integrate PayHere payment gateway
   - Add Stripe for international payments
   - Implement advanced analytics

4. **Long-term (Month 4+)**
   - Social media API integrations
   - Advanced portfolio management
   - Third-party booking platform sync
