import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Database } from '@/lib/supabase/types';

type Booking = Database['public']['Tables']['bookings']['Row'];
type Client = Database['public']['Tables']['clients']['Row'];
type PhotographerProfile = Database['public']['Tables']['photographer_profile']['Row'];

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
}

interface UIState {
  sidebarCollapsed: boolean;
  activeView: string;
  notifications: Notification[];
  loading: {
    bookings: boolean;
    clients: boolean;
    analytics: boolean;
    profile: boolean;
  };
  filters: {
    bookings: {
      status: string[];
      eventType: string[];
      dateRange: {
        from: Date | null;
        to: Date | null;
      };
    };
    clients: {
      search: string;
      sortBy: 'name' | 'created_at' | 'total_bookings';
      sortOrder: 'asc' | 'desc';
    };
  };
}

interface DashboardState {
  // Data
  bookings: Booking[];
  clients: Client[];
  selectedBooking: Booking | null;
  selectedClient: Client | null;
  
  // UI State
  ui: UIState;
  
  // Actions
  actions: {
    // Booking actions
    setBookings: (bookings: Booking[]) => void;
    addBooking: (booking: Booking) => void;
    updateBooking: (id: string, updates: Partial<Booking>) => void;
    selectBooking: (booking: Booking | null) => void;
    
    // Client actions
    setClients: (clients: Client[]) => void;
    addClient: (client: Client) => void;
    updateClient: (id: string, updates: Partial<Client>) => void;
    selectClient: (client: Client | null) => void;
    
    // UI actions
    toggleSidebar: () => void;
    setActiveView: (view: string) => void;
    addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
    markNotificationRead: (id: string) => void;
    clearNotifications: () => void;
    setLoading: (key: keyof UIState['loading'], loading: boolean) => void;
    
    // Filter actions
    setBookingFilters: (filters: Partial<UIState['filters']['bookings']>) => void;
    setClientFilters: (filters: Partial<UIState['filters']['clients']>) => void;
    resetFilters: () => void;
  };
}

const initialUIState: UIState = {
  sidebarCollapsed: false,
  activeView: 'dashboard',
  notifications: [],
  loading: {
    bookings: false,
    clients: false,
    analytics: false,
    profile: false,
  },
  filters: {
    bookings: {
      status: [],
      eventType: [],
      dateRange: {
        from: null,
        to: null,
      },
    },
    clients: {
      search: '',
      sortBy: 'created_at',
      sortOrder: 'desc',
    },
  },
};

export const useDashboardStore = create<DashboardState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        bookings: [],
        clients: [],
        selectedBooking: null,
        selectedClient: null,
        ui: initialUIState,

        actions: {
          // Booking actions
          setBookings: (bookings) => set({ bookings }),
          
          addBooking: (booking) => set((state) => ({
            bookings: [booking, ...state.bookings],
          })),
          
          updateBooking: (id, updates) => set((state) => ({
            bookings: state.bookings.map((booking) =>
              booking.id === id ? { ...booking, ...updates } : booking
            ),
            selectedBooking: state.selectedBooking?.id === id 
              ? { ...state.selectedBooking, ...updates }
              : state.selectedBooking,
          })),
          
          selectBooking: (booking) => set({ selectedBooking: booking }),

          // Client actions
          setClients: (clients) => set({ clients }),
          
          addClient: (client) => set((state) => ({
            clients: [client, ...state.clients],
          })),
          
          updateClient: (id, updates) => set((state) => ({
            clients: state.clients.map((client) =>
              client.id === id ? { ...client, ...updates } : client
            ),
            selectedClient: state.selectedClient?.id === id 
              ? { ...state.selectedClient, ...updates }
              : state.selectedClient,
          })),
          
          selectClient: (client) => set({ selectedClient: client }),

          // UI actions
          toggleSidebar: () => set((state) => ({
            ui: {
              ...state.ui,
              sidebarCollapsed: !state.ui.sidebarCollapsed,
            },
          })),
          
          setActiveView: (view) => set((state) => ({
            ui: {
              ...state.ui,
              activeView: view,
            },
          })),
          
          addNotification: (notification) => set((state) => ({
            ui: {
              ...state.ui,
              notifications: [
                {
                  ...notification,
                  id: crypto.randomUUID(),
                  timestamp: new Date(),
                  read: false,
                },
                ...state.ui.notifications,
              ],
            },
          })),
          
          markNotificationRead: (id) => set((state) => ({
            ui: {
              ...state.ui,
              notifications: state.ui.notifications.map((notification) =>
                notification.id === id ? { ...notification, read: true } : notification
              ),
            },
          })),
          
          clearNotifications: () => set((state) => ({
            ui: {
              ...state.ui,
              notifications: [],
            },
          })),
          
          setLoading: (key, loading) => set((state) => ({
            ui: {
              ...state.ui,
              loading: {
                ...state.ui.loading,
                [key]: loading,
              },
            },
          })),

          // Filter actions
          setBookingFilters: (filters) => set((state) => ({
            ui: {
              ...state.ui,
              filters: {
                ...state.ui.filters,
                bookings: {
                  ...state.ui.filters.bookings,
                  ...filters,
                },
              },
            },
          })),
          
          setClientFilters: (filters) => set((state) => ({
            ui: {
              ...state.ui,
              filters: {
                ...state.ui.filters,
                clients: {
                  ...state.ui.filters.clients,
                  ...filters,
                },
              },
            },
          })),
          
          resetFilters: () => set((state) => ({
            ui: {
              ...state.ui,
              filters: initialUIState.filters,
            },
          })),
        },
      }),
      {
        name: 'photographer-dashboard-store',
        partialize: (state) => ({
          ui: {
            sidebarCollapsed: state.ui.sidebarCollapsed,
            filters: state.ui.filters,
          },
        }),
      }
    ),
    {
      name: 'photographer-dashboard',
    }
  )
);
