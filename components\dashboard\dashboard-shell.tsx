'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/lib/auth/auth-context';
import { useDashboardStore } from '@/lib/store/dashboard-store';
import { useRealtimeSubscriptions } from '@/lib/hooks/use-realtime';
import { DashboardSidebar } from './dashboard-sidebar';
import { DashboardHeader } from './dashboard-header';
import { NotificationCenter } from './notification-center';
import { CommandPalette } from './command-palette';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { cn } from '@/lib/utils';

interface DashboardShellProps {
  children: React.ReactNode;
}

export function DashboardShell({ children }: DashboardShellProps) {
  const { user, profile, loading } = useAuth();
  const pathname = usePathname();
  const { ui, actions } = useDashboardStore();
  const [mounted, setMounted] = useState(false);
  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);
  
  // Initialize realtime subscriptions
  const realtime = useRealtimeSubscriptions();

  // Set mounted state for hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Update active view based on pathname
  useEffect(() => {
    const view = pathname.split('/').pop() || 'dashboard';
    actions.setActiveView(view);
  }, [pathname, actions]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Command palette (Cmd/Ctrl + K)
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setCommandPaletteOpen(true);
      }
      
      // Toggle sidebar (Cmd/Ctrl + B)
      if ((e.metaKey || e.ctrlKey) && e.key === 'b') {
        e.preventDefault();
        actions.toggleSidebar();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [actions]);

  // Show loading spinner during initial load
  if (loading || !mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-rose-50 via-white to-amber-50">
        <div className="text-center space-y-4">
          <LoadingSpinner size="lg" />
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Show error if no user
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-rose-50 via-white to-amber-50">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
          <p className="text-gray-600">Please log in to access the dashboard.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 via-white to-amber-50">
      {/* Background Pattern */}
      <div className="fixed inset-0 opacity-30">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.15)_1px,transparent_0)] bg-[length:20px_20px]" />
      </div>

      {/* Main Layout */}
      <div className="relative flex h-screen overflow-hidden">
        {/* Sidebar */}
        <AnimatePresence mode="wait">
          <motion.div
            key={ui.sidebarCollapsed ? 'collapsed' : 'expanded'}
            initial={{ width: ui.sidebarCollapsed ? 80 : 280 }}
            animate={{ width: ui.sidebarCollapsed ? 80 : 280 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="flex-shrink-0 border-r border-white/20 backdrop-blur-xl bg-white/10"
          >
            <DashboardSidebar collapsed={ui.sidebarCollapsed} />
          </motion.div>
        </AnimatePresence>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <DashboardHeader 
            onToggleSidebar={actions.toggleSidebar}
            onOpenCommandPalette={() => setCommandPaletteOpen(true)}
            realtimeStatus={realtime.isConnected}
          />

          {/* Page Content */}
          <main className="flex-1 overflow-auto p-6 space-y-6">
            <motion.div
              key={pathname}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="h-full"
            >
              {children}
            </motion.div>
          </main>
        </div>
      </div>

      {/* Notification Center */}
      <NotificationCenter />

      {/* Command Palette */}
      <CommandPalette 
        open={commandPaletteOpen}
        onOpenChange={setCommandPaletteOpen}
      />

      {/* Connection Status Indicator */}
      <AnimatePresence>
        {!realtime.isConnected && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed bottom-4 left-4 z-50"
          >
            <div className="glass px-4 py-2 rounded-lg border border-amber-200/50 bg-amber-50/80">
              <div className="flex items-center gap-2 text-amber-800">
                <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium">Reconnecting...</span>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
